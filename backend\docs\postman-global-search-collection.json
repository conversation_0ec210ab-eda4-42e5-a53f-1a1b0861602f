{"info": {"name": "Global Search API Tests", "description": "Collection complète pour tester la recherche globale avec Meilisearch", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "type": "string"}, {"key": "search_query", "value": "<PERSON><PERSON>", "type": "string"}], "item": [{"name": "Global Search", "item": [{"name": "Global Search Simple", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/search?q={{search_query}}", "host": ["{{base_url}}"], "path": ["search"], "query": [{"key": "q", "value": "{{search_query}}"}]}}}, {"name": "Global Search with Pagination", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/search?q={{search_query}}&per_page=5&page=2", "host": ["{{base_url}}"], "path": ["search"], "query": [{"key": "q", "value": "{{search_query}}"}, {"key": "per_page", "value": "5"}, {"key": "page", "value": "2"}]}}}, {"name": "Global Search with Types", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/search?q={{search_query}}&types[]=professional_profiles&types[]=service_offers", "host": ["{{base_url}}"], "path": ["search"], "query": [{"key": "q", "value": "{{search_query}}"}, {"key": "types[]", "value": "professional_profiles"}, {"key": "types[]", "value": "service_offers"}]}}}, {"name": "Global Search with Filters", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/search?q=Developer&filters[city]=Paris&filters[availability_status]=available", "host": ["{{base_url}}"], "path": ["search"], "query": [{"key": "q", "value": "Developer"}, {"key": "filters[city]", "value": "Paris"}, {"key": "filters[availability_status]", "value": "available"}]}}}]}, {"name": "Professional Search", "item": [{"name": "Search Professionals", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/search/professionals?q=Developer", "host": ["{{base_url}}"], "path": ["search", "professionals"], "query": [{"key": "q", "value": "Developer"}]}}}, {"name": "Search Professionals with Filters", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/search/professionals?q=Developer&filters[city]=Paris&filters[min_experience]=3", "host": ["{{base_url}}"], "path": ["search", "professionals"], "query": [{"key": "q", "value": "Developer"}, {"key": "filters[city]", "value": "Paris"}, {"key": "filters[min_experience]", "value": "3"}]}}}]}, {"name": "Service Search", "item": [{"name": "Search Services", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/search/services?q={{search_query}}", "host": ["{{base_url}}"], "path": ["search", "services"], "query": [{"key": "q", "value": "{{search_query}}"}]}}}, {"name": "Search Services with Price Filter", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/search/services?q=Development&filters[max_price]=1000", "host": ["{{base_url}}"], "path": ["search", "services"], "query": [{"key": "q", "value": "Development"}, {"key": "filters[max_price]", "value": "1000"}]}}}]}, {"name": "Achievement Search", "item": [{"name": "Search Achievements", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/search/achievements?q={{search_query}}", "host": ["{{base_url}}"], "path": ["search", "achievements"], "query": [{"key": "q", "value": "{{search_query}}"}]}}}, {"name": "Search Achievements by Organization", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/search/achievements?q=Certified&filters[organization]=Laravel", "host": ["{{base_url}}"], "path": ["search", "achievements"], "query": [{"key": "q", "value": "Certified"}, {"key": "filters[organization]", "value": "<PERSON><PERSON>"}]}}}]}, {"name": "Suggestions & Stats", "item": [{"name": "Get Suggestions", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/search/suggestions?q=Lar&limit=5", "host": ["{{base_url}}"], "path": ["search", "suggestions"], "query": [{"key": "q", "value": "<PERSON>r"}, {"key": "limit", "value": "5"}]}}}, {"name": "Get Statistics", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/search/stats", "host": ["{{base_url}}"], "path": ["search", "stats"]}}}]}, {"name": "Validation Tests", "item": [{"name": "Missing Query (<PERSON> Fail)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/search", "host": ["{{base_url}}"], "path": ["search"]}}}, {"name": "Short Query (Should Fail)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/search?q=a", "host": ["{{base_url}}"], "path": ["search"], "query": [{"key": "q", "value": "a"}]}}}, {"name": "Invalid Type (Should Fail)", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/search?q=test&types[]=invalid_type", "host": ["{{base_url}}"], "path": ["search"], "query": [{"key": "q", "value": "test"}, {"key": "types[]", "value": "invalid_type"}]}}}]}]}