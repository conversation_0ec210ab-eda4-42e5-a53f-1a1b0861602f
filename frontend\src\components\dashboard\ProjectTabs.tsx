import React from 'react';
import './ProjectTabs.css';

interface ProjectTabsProps {
  activeTab?: 'open' | 'ongoing' | 'completed' | 'canceled' | 'standby';
  onTabChange?: (tab: 'open' | 'ongoing' | 'completed' | 'canceled' | 'standby') => void;
  projects: { status: string }[];
  isClientProfile?: boolean;
}

const ProjectTabs: React.FC<ProjectTabsProps> = ({
  activeTab = 'open',
  onTabChange,
  projects,
  isClientProfile,
}) => {
  const counts = {
    open: projects.filter(p => p.status === 'open').length,
    ongoing: projects.filter(p => p.status === 'in_progress').length,
    completed: projects.filter(p => p.status === 'completed').length,
  };

  return (
    <div className="project-tabs-container">
      <h1 className="project-tabs-title py-10 text-2xl font-bold">My Projects</h1>
      <div className="project-tabs">
        <button
          className={`project-tab ${activeTab === 'open' ? 'disabled' : 'active'}`}
          onClick={() => onTabChange && onTabChange('open')}
        >
          Open offers <span className="tab-count">{counts.open}</span>
        </button>
        <button
          className={`project-tab ${activeTab === 'ongoing' ? 'disabled' : 'active'}`}
          onClick={() => onTabChange && onTabChange('ongoing')}
          
        >
          Ongoing project <span className="tab-count">{counts.ongoing}</span>
        </button>
        <button
          className={`project-tab ${activeTab === 'completed' ? 'disabled' : 'active'}`}
          onClick={() => onTabChange && onTabChange('completed')}
          
        >
          Completed project <span className="tab-count">{counts.completed}</span>
        </button>
        {isClientProfile && (
          <>
            <button
              className="project-tab"
              onClick={() => onTabChange && onTabChange('canceled' as any)}
            >
              Canceled
            </button>
            <button
              className="project-tab"
              onClick={() => onTabChange && onTabChange('standby' as any)}
            >
              Stand By
            </button>
          </>
        )}
      </div>
      <div className="project-tabs-underline" />
    </div>
  );
};

export default ProjectTabs;

// import React from 'react';
// import './ProjectTabs.css';

// interface ProjectTabsProps {
//   activeTab?: 'open' | 'ongoing' | 'completed';
//   onTabChange?: (tab: 'open' | 'ongoing' | 'completed') => void;
//   counts?: { open: number; ongoing: number; completed: number };
//   projects: { status: string }[];
// }

// const ProjectTabs: React.FC<ProjectTabsProps> = ({
//   activeTab = 'open',
//   onTabChange,
//   // counts = { open: 2, ongoing: 7, completed: 7 },
//   projects,
//   counts = {
//   open: projects.filter(p => p.status === 'open').length,
//   ongoing: projects.filter(p => p.status === 'in_progress').length,
//   completed: projects.filter(p => p.status === 'completed').length,
//   },
// }) => {
//   return (
//     <div className="project-tabs-container">
//       <h1 className="project-tabs-title py-10 text-2xl font-bold">My Projects</h1>
//       <div className="project-tabs">
//         <button
//           className={`project-tab ${activeTab === 'open' ? 'active' : 'disabled'}`}
//           onClick={() => onTabChange && onTabChange('open')}
//         >
//           Open offers <span className="tab-count">{counts.open}</span>
//         </button>
//         <button
//           className={`project-tab ${activeTab === 'ongoing' ? 'active' : 'disabled'}`}
//           onClick={() => onTabChange && onTabChange('ongoing')}
//           disabled
//         >
//           Ongoing project <span className="tab-count">{counts.ongoing}</span>
//         </button>
//         <button
//           className={`