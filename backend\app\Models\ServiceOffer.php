<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
// use Laravel\Scout\Searchable;

class ServiceOffer extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'title',
        'description',
        'price',
        'execution_time',
        'concepts',
        'revisions',
        'is_private',
        'status',
        'categories',
        'files',
        'views',
        'likes',
        'rating',
        'image',
    ];

    protected $casts = [
        'categories' => 'array', // Cast 'categories' to array
        'files' => 'array',      // Cast 'files' to array
        'is_private' => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    // public function toSearchableArray()
    // {
    //     return [
    //         'title' => $this->title,
    //         'description' => $this->description,
    //         // Add other fields you want to be searchable
    //     ];
    // }
}
