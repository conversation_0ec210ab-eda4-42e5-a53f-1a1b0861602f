import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Briefcase,
  DollarSign,
  Users,
  Clock,
  MessageSquare,
  Plus,
  FileText,
  Bell,
  CheckCircle,
  XCircle,
  Calendar,
  Search,
  Star,
  Send,
  Filter,
  AlertTriangle,
  ChevronLeft,
  ChevronRight,
  SlidersHorizontal
} from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import Button from '../ui/Button';
import ActivityItem, { ActivityItemProps } from '../dashboard/ActivityItem';
import Badge from '../ui/Badge';

// Étendre l'interface ActivityItemProps pour inclure le type
interface ExtendedActivityItemProps extends ActivityItemProps {
  type?: string;
}

const AllActivitiesPage: React.FC = () => {
  const navigate = useNavigate();
  const [activities, setActivities] = useState<ExtendedActivityItemProps[]>([]);
  const [filteredActivities, setFilteredActivities] = useState<ExtendedActivityItemProps[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const token = localStorage.getItem('token');

  useEffect(() => {
    const fetchActivities = async () => {
      setLoading(true);
      try {
        const response = await fetch(`${API_BASE_URL}/api/activities`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des activités');
        }

        const data = await response.json();
        console.log("Tous les activités : ",data);
        // Transformer les données en format ActivityItemProps
        const formattedActivities: ExtendedActivityItemProps[] = data.activities.map((activity: any) => {
          // Déterminer l'icône appropriée en fonction du type d'activité
          // let icon;
          let iconBackground = '';
          let iconColor = '';


          let icon;
            switch(activity.icon) {
              case 'MessageSquare':
                icon = <MessageSquare className="h-5 w-5" />;
                break;
              case 'CheckCircle':
                icon = <CheckCircle className="h-5 w-5" />;
                break;
              case 'Star':
                icon = <Star className="h-5 w-5" />;
                break;
              case 'DollarSign':
                icon = <DollarSign className="h-5 w-5" />;
                break;
              case 'Briefcase':
                icon = <Briefcase className="h-5 w-5" />;
                break;
              case 'Users':
                icon = <Users className="h-5 w-5" />;
                break;
              default:
                icon = <Bell className="h-5 w-5" />;
            }
          // switch(activity.type) {
          //   case 'message':
          //     icon = 'MessageSquare';
          //     break;
          //   case 'project_completed':
          //     icon = 'CheckCircle';
          //     iconBackground = 'bg-green-100';
          //     iconColor = 'text-green-600';
          //     break;
          //   case 'review':
          //     icon = 'Star';
          //     iconBackground = 'bg-yellow-100';
          //     iconColor = 'text-yellow-600';
          //     break;
          //   case 'payment':
          //     icon = 'DollarSign';
          //     iconBackground = 'bg-blue-100';
          //     iconColor = 'text-blue-600';
          //     break;
          //   case 'connection':
          //     icon = 'Users';
          //     iconBackground = 'bg-purple-100';
          //     iconColor = 'text-purple-600';
          //     break;
          //   case 'project':
          //     icon = 'Briefcase';
          //     break;
          //   default:
          //     icon = 'Bell';
          // }

          return {
            id: activity.id,
            title: activity.title,
            description: activity.description,
            timestamp: activity.timestamp,
            icon: icon,
            iconBackground: iconBackground || undefined,
            iconColor: iconColor || undefined,
            user: activity.user ? {
              name: activity.user.name,
              avatar: `${API_BASE_URL}${activity.user.avatar}`,
            } : undefined,
            type: activity.type,
          };
        });

        setActivities(formattedActivities);
        setFilteredActivities(formattedActivities);
        setError(null);
      } catch (err) {
        console.error('Error fetching activities:', err);
        setError('Impossible de récupérer les activités. Veuillez réessayer plus tard.');
        // Utiliser des données statiques en cas d'erreur
        const mockActivities = getMockActivities();
        setActivities(mockActivities);
        setFilteredActivities(mockActivities);
      } finally {
        setLoading(false);
      }
    };

    fetchActivities();
  }, [token]);

  // Filtrer les activités lorsque les filtres changent
  useEffect(() => {
    let result = [...activities];

    // Filtre par recherche
    if (searchTerm) {
      result = result.filter(activity =>
        (typeof activity.title === 'string' ? activity.title.toLowerCase().includes(searchTerm.toLowerCase()) : false) ||
        (activity.description && typeof activity.description === 'string' ? activity.description.toLowerCase().includes(searchTerm.toLowerCase()) : false)
      );
    }

    // Filtre par type
    if (typeFilter !== 'all') {
      result = result.filter(activity => activity.type === typeFilter);
    }

    // Filtre par date
    if (dateFilter !== 'all') {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      switch (dateFilter) {
        case 'today':
          result = result.filter(activity => {
            const activityDate = new Date(activity.timestamp);
            return activityDate >= today;
          });
          break;
        case 'week':
          const weekAgo = new Date(today);
          weekAgo.setDate(weekAgo.getDate() - 7);
          result = result.filter(activity => {
            const activityDate = new Date(activity.timestamp);
            return activityDate >= weekAgo;
          });
          break;
        case 'month':
          const monthAgo = new Date(today);
          monthAgo.setMonth(monthAgo.getMonth() - 1);
          result = result.filter(activity => {
            const activityDate = new Date(activity.timestamp);
            return activityDate >= monthAgo;
          });
          break;
      }
    }

    setFilteredActivities(result);
    setCurrentPage(1); // Réinitialiser à la première page après filtrage
  }, [searchTerm, typeFilter, dateFilter, activities]);

  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredActivities.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredActivities.length / itemsPerPage);

  const paginate = (pageNumber: number) => {
    if (pageNumber > 0 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };

  // Données statiques pour le fallback
  const getMockActivities = (): ExtendedActivityItemProps[] => {
    return [
      {
        id: 1,
        title: 'Nouveau message reçu',
        description: 'Vous avez reçu un message concernant le projet "Création d\'un personnage 3D"',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        icon: 'MessageSquare',
        user: {
          name: 'Thomas Martin',
          avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
        },
        type: 'message',
      },
      {
        id: 2,
        title: 'Projet terminé',
        description: 'Le projet "Modélisation d\'objets pour environnement virtuel" a été marqué comme terminé',
        timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        icon: 'CheckCircle',
        iconBackground: 'bg-green-100',
        iconColor: 'text-green-600',
        type: 'project_completed',
      },
      {
        id: 3,
        title: 'Nouvelle évaluation reçue',
        description: 'Vous avez reçu une évaluation 5 étoiles pour votre travail',
        timestamp: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
        icon: 'Star',
        iconBackground: 'bg-yellow-100',
        iconColor: 'text-yellow-600',
        user: {
          name: 'VR Experiences',
          avatar: 'https://randomuser.me/api/portraits/men/67.jpg',
        },
        type: 'review',
      },
      {
        id: 4,
        title: 'Paiement reçu',
        description: '500 € reçus pour "Modélisation d\'objets pour environnement virtuel"',
        timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        icon: 'DollarSign',
        iconBackground: 'bg-blue-100',
        iconColor: 'text-blue-600',
        type: 'payment',
      },
      {
        id: 5,
        title: 'Nouveau professionnel connecté',
        description: 'Sophie Dubois a accepté votre offre pour "Animation d\'une scène d\'introduction"',
        timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        icon: 'Users',
        iconBackground: 'bg-purple-100',
        iconColor: 'text-purple-600',
        user: {
          name: 'Sophie Dubois',
          avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
        },
        type: 'connection',
      },
      {
        id: 6,
        title: 'Nouveau projet créé',
        description: 'Vous avez créé un nouveau projet "Logo 3D pour entreprise"',
        timestamp: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
        icon: 'Briefcase',
        type: 'project',
      },
    ];
  };

  return (
    <DashboardLayout
      title="Historique d'activités"
      subtitle="Consultez toutes vos activités récentes"
      actions={
        <Button
          variant="outline"
          onClick={() => navigate('/dashboard')}
        >
          Retour au tableau de bord
        </Button>
      }
    >
      {/* Barre de recherche et filtres */}
      <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Rechercher une activité..."
              className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <select
              className="px-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              aria-label="Filtrer par type d'activité"
            >
              <option value="all">Tous les types</option>
              <option value="message">Messages</option>
              <option value="project">Projets</option>
              <option value="project_completed">Projets terminés</option>
              <option value="payment">Paiements</option>
              <option value="review">Évaluations</option>
              <option value="connection">Connexions</option>
            </select>
            <select
              className="px-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              aria-label="Filtrer par période"
            >
              <option value="all">Toutes les dates</option>
              <option value="today">Aujourd'hui</option>
              <option value="week">Cette semaine</option>
              <option value="month">Ce mois-ci</option>
            </select>
            <Button
              variant="outline"
              leftIcon={<SlidersHorizontal className="h-5 w-5" />}
              onClick={() => setShowFilters(!showFilters)}
            >
              Plus
            </Button>
          </div>
        </div>

        {/* Filtres avancés */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-neutral-200 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">Période personnalisée</label>
              <div className="flex gap-2">
                <input
                  type="date"
                  className="flex-1 px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  aria-label="Date de début"
                  placeholder="Date de début"
                />
                <span className="flex items-center text-neutral-500">à</span>
                <input
                  type="date"
                  className="flex-1 px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  aria-label="Date de fin"
                  placeholder="Date de fin"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">Utilisateur</label>
              <input
                type="text"
                placeholder="Filtrer par utilisateur..."
                className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
          </div>
        )}
      </div>

      {/* Liste des activités */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-lg font-semibold text-red-700 mb-2">Erreur</h2>
          <p className="text-red-600">{error}</p>
          <Button
            variant="primary"
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Réessayer
          </Button>
        </div>
      ) : filteredActivities.length === 0 ? (
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-8 text-center">
          <Clock className="h-16 w-16 text-neutral-300 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-neutral-800 mb-2">Aucune activité trouvée</h2>
          <p className="text-neutral-600 mb-6">
            Aucune activité ne correspond à vos critères de recherche. Essayez de modifier vos filtres.
          </p>
        </div>
      ) : (
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-neutral-200 flex justify-between items-center">
            <h3 className="text-lg font-semibold text-neutral-900">
              {filteredActivities.length} activité{filteredActivities.length > 1 ? 's' : ''}
            </h3>
            <div className="flex items-center text-sm text-neutral-500">
              <span>Affichage de {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, filteredActivities.length)} sur {filteredActivities.length}</span>
            </div>
          </div>

          <div className="divide-y divide-neutral-200">
            {currentItems.map((activity) => (
              <ActivityItem key={activity.id} {...activity} />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-6 py-4 border-t border-neutral-200 flex justify-between items-center">
              <Button
                variant="ghost"
                size="sm"
                leftIcon={<ChevronLeft className="h-4 w-4" />}
                onClick={() => paginate(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Précédent
              </Button>
              <div className="flex items-center space-x-2">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((number) => (
                  <button
                    key={number}
                    type="button"
                    onClick={() => paginate(number)}
                    className={`px-3 py-1 rounded-md ${
                      currentPage === number
                        ? 'bg-primary-600 text-white'
                        : 'text-neutral-600 hover:bg-neutral-100'
                    }`}
                  >
                    {number}
                  </button>
                ))}
              </div>
              <Button
                variant="ghost"
                size="sm"
                rightIcon={<ChevronRight className="h-4 w-4" />}
                onClick={() => paginate(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Suivant
              </Button>
            </div>
          )}
        </div>
      )}
    </DashboardLayout>
  );
};

export default AllActivitiesPage;
