import React from 'react';
import { MoreHorizontal } from 'lucide-react';
import Avatar from '../ui/Avatar';
import Button from '../ui/Button';
import Badge from '../ui/Badge';
import { getAvatarUrl, getInitials } from '../../utils/avatarUtils';

interface CivilityStateProps {
  profile: {
    first_name: string;
    last_name: string;
    city?: string;
    country?: string;
    avatar?: string;
  };
  onEditProfile: () => void;
}

const CivilityState: React.FC<CivilityStateProps> = ({ profile, onEditProfile }) => {
  const avatarUrl = getAvatarUrl(profile.avatar);
  const initials = getInitials(profile.first_name, profile.last_name);

  return (
    <div className="flex items-start p-4 mb-15 mt-20">
      <div className="flex-shrink-0">
        <Avatar size="xxl" src={avatarUrl} fallback={initials} />
      </div>
      <div className="ml-4 flex flex-col justify-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">{profile.first_name} {profile.last_name}</h1>
        <p className="text-md text-black mb-2">
          {profile.city && profile.country
            ? `${profile.city}, ${profile.country}`
            : 'Location not provided'}
        </p>
        <div className="flex items-center space-x-2 mt-2">
          <Button variant="outline" onClick={onEditProfile} className="text-black font-bold">
            Edit my portfolio
          </Button>
          <Button variant="outline" size="icon">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
          <Badge className="bg-[#e2e1f2] text-[#5851ca] rounded-full px-8 py-2 font-extrabold uppercase text-base">
            LIMITED ACCOUNT
          </Badge>
        </div>
      </div>
    </div>
  );
};

export default CivilityState; 