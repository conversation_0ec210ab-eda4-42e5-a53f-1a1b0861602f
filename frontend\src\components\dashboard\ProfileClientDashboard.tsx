import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Mail,
  Phone,
  MapPin,
  Briefcase,
  Download,
  FileText
} from 'lucide-react';
import DashboardLayout from './DashboardLayout';
import Button from '../ui/Button';
import Tabs from '../ui/Tabs';
import { profileService } from '../../services/profileService';
import { API_BASE_URL } from '../../config';
import './ProfileDashboard.css';
import CivilityState from './CivilityState';
import ProjectTabs from './ProjectTabs';
import ProjectCards from './ProjectCards';
import ProjectCard, { ProjectCardProps } from './ProjectCard';

interface ProfileDataLocal {
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  bio?: string;
  skills?: string[];
  avatar?: string;
  portfolio?: Array<{
    path: string;
    name: string;
    type: string;
  }>;
  title?: string;
  rating?: number;
  hourly_rate?: number;
  availability_status?: string;
  experience?: number;
  completion_percentage: number;
  languages?: string[];
  services_offered?: string[];
}

const ProfileDashboard: React.FC = () => {
  const [profileData, setProfileData] = useState<ProfileDataLocal | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [projects, setProjects] = useState<ProjectCardProps[]>([]);
  const [activeTab, setActiveTab] = useState<'open' | 'ongoing' | 'completed' | 'canceled' | 'standby'>('open');
  // Supprimé car nous utilisons maintenant le composant Tabs
  const navigate = useNavigate();

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const token = localStorage.getItem('token');

  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        if (!token) {
          navigate('/login');
          return;
        }

        const { profile } = await profileService.getProfile();
        console.log('Profile data from API:', profile);
        setProfileData(profile as unknown as ProfileDataLocal);
        setError(null);
      } catch (err) {
        console.error('Error fetching profile data:', err);
        setError('Failed to fetch profile data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, [navigate,token]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      try {
        // Fetch dashboard data from API
        const dashboardResponse = await fetch(`${API_BASE_URL}/api/dashboard`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });


        if (!dashboardResponse.ok) {
          // Si l'API renvoie une erreur, utiliser des données statiques
          console.warn('Fallback to static data due to API error:', dashboardResponse.status);
          return;
        }

        const dashboardData = await dashboardResponse.json();
        localStorage.setItem('userProfile', JSON.stringify(dashboardData.profile));

        // Set projects
        setProjects(dashboardData.projects.map((project: any) => ({
          id: project.id,
          title: project.title,
          description: project.description,
          budget: project.budget,
          deadline: project.deadline,
          status: project.status,
          client: project.client,
        })));

        setError(null);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
      } finally {
        setLoading(false);
      }
    };


    fetchDashboardData();
  }, [token]);

  const handleEditProfile = () => {
    navigate('/dashboard/profile/edit');
  };

  const handleEditPortfolio = () => {
    navigate('/edit-portfolio');
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <div className="text-red-600 mb-4">{error}</div>
          <Button
            variant="primary"
            onClick={() => window.location.reload()}
          >
            Réessayer
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  if (!profileData) {
    return (
      <DashboardLayout>
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
          <div className="text-yellow-700 mb-4">Aucune donnée de profil disponible. Veuillez compléter votre profil.</div>
          <Button
            variant="primary"
            onClick={handleEditProfile}
          >
            Compléter le profil
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <CivilityState profile={profileData} onEditProfile={handleEditPortfolio} />
      <div style={{ marginTop: 32 }}>
        <ProjectTabs projects={projects} activeTab={activeTab}
        onTabChange={setActiveTab} isClientProfile={true} />
        {(activeTab === 'open' || activeTab === 'ongoing' || activeTab === 'completed') && (
          <ProjectCards projects={projects} activeTab={activeTab}/>
        )}
      </div>
    </DashboardLayout>
  );
};

export default ProfileDashboard;
