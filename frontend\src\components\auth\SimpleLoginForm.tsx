import React, { useState } from 'react';
import { API_BASE_URL } from '../../config';
import './AuthButton.css';

const SimpleLoginForm: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [response, setResponse] = useState<any>(null);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccess('');
    setResponse(null);

    try {
      // Test de connexion au serveur
      try {
        // On vérifie simplement si le serveur est accessible
        // Nous utilisons une requête OPTIONS qui est généralement acceptée par les serveurs
        await fetch(`${API_BASE_URL}/health-check`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        });
        // Même si la réponse n'est pas OK, le serveur est accessible si nous avons reçu une réponse

        setSuccess('Connexion au serveur réussie. Tentative de connexion...');
      } catch (pingError) {
        setError(`Impossible de se connecter au serveur: ${pingError instanceof Error ? pingError.message : 'Erreur inconnue'}`);
        setIsLoading(false);
        return;
      }

      // Tentative de connexion
      const loginResponse = await fetch(`${API_BASE_URL}/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await loginResponse.json();
      setResponse(data);

      if (loginResponse.ok) {
        setSuccess('Connexion réussie!');

        if (data.token && data.user) {
          localStorage.setItem('token', data.token);
          localStorage.setItem('user', JSON.stringify(data.user));

          // Redirection après 2 secondes
          setTimeout(() => {
            window.location.href = '/dashboard';
          }, 2000);
        }
      } else {
        setError(`Erreur de connexion: ${data.message || 'Erreur inconnue'}`);
      }
    } catch (err) {
      setError(`Erreur: ${err instanceof Error ? err.message : 'Erreur inconnue'}`);
      console.error('Erreur complète:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto p-8 bg-white rounded-xl shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-center text-primary-800">Formulaire de connexion simplifié</h2>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-300 text-red-700 rounded">
          {error}
        </div>
      )}

      {success && (
        <div className="mb-4 p-3 bg-green-100 border border-green-300 text-green-700 rounded">
          {success}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label className="block text-gray-700 mb-2">Email</label>
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="<EMAIL>"
            title="Votre adresse email"
            required
          />
        </div>

        <div className="mb-6">
          <label className="block text-gray-700 mb-2">Mot de passe</label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="••••••••"
            title="Votre mot de passe"
            required
          />
        </div>

        <button
          type="submit"
          className="w-full bg-primary-600 py-2 px-4 rounded hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 shadow-sm auth-button"
          disabled={isLoading}
          style={{ color: 'black' }}
        >
          {isLoading ? 'Connexion en cours...' : 'Se connecter'}
        </button>
      </form>

      {response && (
        <div className="mt-6">
          <h3 className="font-bold mb-2">Réponse du serveur:</h3>
          <pre className="bg-gray-100 p-3 rounded overflow-auto text-xs">
            {JSON.stringify(response, null, 2)}
          </pre>
        </div>
      )}

      <div className="mt-8 text-center">
        <p>
          <a href="/login" className="text-primary-600 hover:text-primary-700 font-medium transition-colors">
            Retour au formulaire normal
          </a>
        </p>
      </div>
    </div>
  );
};

export default SimpleLoginForm;
