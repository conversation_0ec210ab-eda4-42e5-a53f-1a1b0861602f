import React, { useState, useEffect } from 'react';
import Header from './Header';
import Footer from './Footer';
import MyWork from './MyWork';
import MyServiceOffer from './MyServiceOffer'
import { API_BASE_URL } from '../config';
import { useNavigate } from 'react-router-dom';
import { profileService } from '../services/profileService';
import type { ServiceOffer, ServiceOfferFormData } from './services/types';

interface AchievementFile {
  path: string;
  original_name: string;
  mime_type: string;
  size: number;
}

interface Achievement {
  id: number;
  professional_profile_id: number; // <- corrigé ici
  title: string;
  organization: string;
  date_obtained: string;
  description: string;
  file_path: string | null;
  files: AchievementFile[];
  achievement_url: string | null;
  created_at?: string;
  updated_at?: string;
}

interface ProfileDataLocal {
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  bio?: string;
  skills?: string[];
  avatar?: string;
  portfolio?: Array<{
    path: string;
    name: string;
    type: string;
  }>;
  title?: string;
  rating?: number;
  hourly_rate?: number;
  availability_status?: string;
  experience?: number;
  completion_percentage: number;
  languages?: string[];
  services_offered?: string[];
}

// Composant inspiré de CivilityState pour l'entête "My Portfolio"
const PortfolioHeader: React.FC = () => {
  // useEffect(() => {
  //     fetchAchievements();
  //   }, []);

  return (
    <div className="mt-10 mb-6">
      <h1 style={{ fontSize: 40, fontWeight: 700, color: '#2D2D2D', marginBottom: 24, fontFamily: 'Arial, sans-serif' }}>
        My Portfolio
      </h1>
      <button
        style={{
          border: '1px solid #E5E5E5',
          borderRadius: 24,
          padding: '12px 24px',
          background: '#fff',
          color: '#222',
          fontWeight: 500,
          fontSize: 16,
          fontFamily: 'Arial, sans-serif',
          cursor: 'pointer',
          boxShadow: 'none',
        }}
      >
        View my portfolio page
      </button>
    </div>
  );
};


const ProfileTabs: React.FC<{ activeTab: string; onTabChange: (tab: 'work' | 'offers' | 'about') => void }> = ({ activeTab, onTabChange }) => (
  <div className="mb-8">
    <div style={{ display: 'flex', gap: 32, borderBottom: '1.5px solid #222', width: '100%', maxWidth: 1200 }}>
      {['work', 'offers', 'about'].map((tab) => (
        <button
          key={tab}
          onClick={() => onTabChange(tab as 'work' | 'offers' | 'about')}
          style={{
            background: 'none',
            border: 'none',
            borderBottom: activeTab === tab ? '2.5px solid #222' : 'none',
            color: activeTab === tab ? '#222' : '#888',
            fontWeight: activeTab === tab ? 600 : 400,
            fontSize: 15,
            padding: '12px 0',
            cursor: 'pointer',
          }}
        >
          {tab === 'work' ? 'My Work' : tab === 'offers' ? 'My Offers' : 'About Me'}
        </button>
      ))}
    </div>
  </div>
);

// Composant inspiré de ProjectTabs pour les onglets
// const ProfileTabs: React.FC = () => {
//   const [activeTab, setActiveTab] = useState<'work' | 'offers' | 'about'>('work');

//   return (
//     <div className="mb-8">
//       <div style={{ display: 'flex', gap: 32, borderBottom: '1.5px solid #222', width: '100%', maxWidth: 1200 }}>
//         <button
//           onClick={() => setActiveTab('work')}
//           style={{
//             background: 'none',
//             border: 'none',
//             borderBottom: activeTab === 'work' ? '2.5px solid #222' : 'none',
//             color: activeTab === 'work' ? '#222' : '#888',
//             fontWeight: activeTab === 'work' ? 600 : 400,
//             fontSize: 15,
//             padding: '12px 0',
//             cursor: 'pointer',
//             outline: 'none',
//             transition: 'color 0.2s',
//           }}
//         >
//           My work
//         </button>
//         <button
//           onClick={() => setActiveTab('offers')}
//           style={{
//             background: 'none',
//             border: 'none',
//             borderBottom: activeTab === 'offers' ? '2.5px solid #222' : 'none',
//             color: activeTab === 'offers' ? '#222' : '#888',
//             fontWeight: activeTab === 'offers' ? 600 : 400,
//             fontSize: 15,
//             padding: '12px 0',
//             cursor: 'pointer',
//             outline: 'none',
//             transition: 'color 0.2s',
//           }}
//         >
//           My offers
//         </button>
//         <button
//           onClick={() => setActiveTab('about')}
//           style={{
//             background: 'none',
//             border: 'none',
//             borderBottom: activeTab === 'about' ? '2.5px solid #222' : 'none',
//             color: activeTab === 'about' ? '#222' : '#888',
//             fontWeight: activeTab === 'about' ? 600 : 400,
//             fontSize: 15,
//             padding: '12px 0',
//             cursor: 'pointer',
//             outline: 'none',
//             transition: 'color 0.2s',
//           }}
//         >
//           About me
//         </button>
//       </div>
//       {/* Optionally, you can add tab content here if needed */}
//     </div>
//   );
// };

// Définir les données ici :
const galleryItems = [
  { 
    id: 1, 
    title: 'SIRIUS - Space Station', 
    author: 'Helen Jhones', 
    authorAvatar: 'https://randomuser.me/api/portraits/women/44.jpg', 
    isPro: true, 
    likes: 35, 
    views: '3.3k', 
    image: 'https://images.unsplash.com/photo-1446776811953-b23d57bd21aa?w=800&h=600&fit=crop'
  },
  { 
    id: 2, 
    title: 'Purple Lake Environment', 
    author: 'Andrey Prokopenko', 
    authorAvatar: 'https://randomuser.me/api/portraits/men/32.jpg', 
    isPro: false, 
    likes: 83, 
    views: '18.9k', 
    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop'
  },
  { 
    id: 3, 
    title: 'Your Health - UI', 
    author: 'Ronas IT | UI/UX Team', 
    authorAvatar: 'https://randomuser.me/api/portraits/men/45.jpg', 
    isPro: false, 
    likes: 59, 
    views: '3.9k', 
    image: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&h=600&fit=crop'
  },
  { 
    id: 4, 
    title: 'Oxford AR', 
    author: 'Plainthing Studio', 
    authorAvatar: 'https://randomuser.me/api/portraits/men/23.jpg', 
    isPro: false, 
    likes: 63, 
    views: '6.3k', 
    image: 'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800&h=600&fit=crop'
  }
];

const portfolioContainerStyle = {
  maxWidth: 1400,
  margin: '0 auto',
  paddingLeft: 16,
  paddingRight: 16,
};

const EditComponent: React.FC = () => {
  const navigate = useNavigate();
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [services, setServices] = useState<any[]>([]);
  const [servicesFix, setServicesFix] = useState<ServiceOffer[]>([]);
  const [bio, setBio] = useState<string | null>(null);
  const [avatar, setAvatar] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'work' | 'offers' | 'about'>('work');

  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [profileData, setProfileData] = useState<ProfileDataLocal | null>(null);
  const token = localStorage.getItem("token");
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  const isPro = user.is_professional;

  const fetchProfileData = async () => {
        try {
          if (!token) {
            navigate('/login');
            return;
          }
  
          const { profile } = await profileService.getProfile();
          console.log('Profile data from API:', profile);
          setProfileData(profile as unknown as ProfileDataLocal);
          setBio(profile?.bio|| null);
          setAvatar(profile?.avatar|| null)
          setError(null);
        } catch (err) {
          console.error('Error fetching profile data:', err);
          setError('Failed to fetch profile data. Please try again later.');
        } finally {
          setLoading(false);
        }
      };   

  const fetchAchievements = async () => {
      try {
        setLoading(true);
        if (!token) {
          navigate('/login');
          return;
        }
  
        const response = await fetch(`${API_BASE_URL}/api/achievements`, {
          method: "GET",
          headers: { "Authorization": `Bearer ${token}` }
        });
  
        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des réalisations');
        }
  
        const data = await response.json();
        setAchievements(data.achievements || []);
        setLoading(false);
      } catch (error) {
        console.error("Erreur lors de la récupération des réalisations", error);
        setError("Impossible de charger vos réalisations. Veuillez réessayer plus tard.");
        setLoading(false);
      }
    };

    const fetchServices = async () => {
      try {
        setLoading(true);
        if (!token) {
          navigate('/login');
          return;
        }

        const userId = user.id;//userIdParam || 
  
        const response = await fetch(`${API_BASE_URL}/api/professionals/${userId}/service-offers`, {
          method: "GET",
          headers: { "Authorization": `Bearer ${token}` }
        });
  
        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des service offert');
        }
  
        const data = await response.json();
        setServices(data || []);


        console.log("Réponse API complète:", data);
        console.log("Type de la réponse API:", typeof data);

        // Vérifier si data est un tableau ou un objet avec une propriété data
        let apiServices = [];

        // Vérifier si data est un tableau
        if (Array.isArray(data)) {
          console.log("La réponse API est un tableau");
          apiServices = data;
        }
        // Vérifier si data est un objet avec une propriété data
        else if (data && typeof data === 'object') {
          console.log("La réponse API est un objet");

          // Vérifier si data.data existe et est un tableau
          if (data.data && Array.isArray(data.data)) {
            console.log("La réponse API contient une propriété data qui est un tableau");
            apiServices = data.data;
          }
          // Sinon, essayer d'utiliser data directement
          else {
            console.log("Utilisation de data directement");
            apiServices = [data];
          }
        }

        console.log("Services bruts de l'API:", apiServices.length);

        // Vérifier la structure des services
        if (apiServices.length > 0) {
          console.log("Structure du premier service:", JSON.stringify(apiServices[0], null, 2));
        }

        console.log("Services pour l'utilisateur", userId, ":", apiServices.length);

        // Transformer les données de l'API
        const formattedServices = apiServices.map((service: any) => ({
          id: service.id,
          user_id: service.user_id,
          title: service.title,
          description: service.description,
          price: service.price,
          execution_time: service.execution_time,
          concepts: service.concepts,
          revisions: service.revisions,
          categories: typeof service.categories === 'string' ? JSON.parse(service.categories) : (service.categories || []),
          is_private: service.is_private !== false,
          status: service.status || 'published',
          created_at: service.created_at,
          updated_at: service.updated_at,
          user: service.user ? {
            id: service.user.id,
            first_name: service.user.first_name,
            last_name: service.user.last_name,
            profile_picture_path: service.user.profile_picture_path,
          } : undefined,
          files: service.files || [],
          imageUrl: service.files && service.files.length > 0
            ? `${API_BASE_URL}/storage/${service.files[0].path}`
            : 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          file_urls: Array.isArray(service.files)
            ? service.files.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
            : [],
          likes: service.likes || 0,
          views: service.views || 0,
        }));

        setServicesFix(formattedServices);

        console.log("Services récupérés:", formattedServices.length);
        setLoading(false);
      } catch (error) {
        console.error("Erreur lors de la récupération des services", error);
        setError("Impossible de charger vos service. Veuillez réessayer plus tard.");
        setLoading(false);
      }
    };
  useEffect(() => {
    // fetchData();
    fetchAchievements();
    fetchServices();
    fetchProfileData();
  }, [token]);
  
  return (
    <div style={{ background: '#fff', minHeight: '100vh' }}>
      <Header />
      <main style={{ minHeight: '70vh' }}>
        <div className="px-4 md:px-20 w-full max-w-[1512px] mx-auto">
          <PortfolioHeader />
          <ProfileTabs activeTab={activeTab} onTabChange={setActiveTab} />
          <div style={{ marginTop: 45, marginBottom: 450 }}>
            {loading ? (
              <p>Chargement...</p>
            ) : activeTab === 'work' ? (
              <MyWork items={achievements.map((ach, index) => ({
                id: ach.id,
                title: ach.title,
                author: ach.organization || 'Unknown',
                authorAvatar: `${API_BASE_URL}${avatar}`||'https://randomuser.me/api/portraits/lego/1.jpg',
                likes: 0,
                views: '0',
                isPro: isPro,
                image: `${API_BASE_URL}/storage/${ach.files[0].path}` || 'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800&h=600&fit=crop',
              }))} />
            ) : activeTab === 'offers' ? (
              <MyServiceOffer items={services.map((service, index) => ({
                  id: service.id,
                  title: service.title,
                  author: service.title || 'Unknown',
                  authorAvatar: `${API_BASE_URL}${service.user.professional_details.avatar}` ||'https://randomuser.me/api/portraits/lego/1.jpg', // tu peux mettre un vrai avatar
                  likes: 0,
                  views: '0',
                  isPro: isPro,
                  image: `${API_BASE_URL}/storage/${service.files?.[0]?.path}` || 'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800&h=600&fit=crop', // ou une image par défaut
                }))}
                service={servicesFix}
               />
              
            ) : (
              <div className="prose max-w-none text-gray-800 text-md">
                {bio ? <p>{bio}</p> : <p>Aucune biographie disponible.</p>}
              </div>
            )}
          </div>

          {/* <ProfileTabs />
          <div style={{ marginTop: 45, marginBottom: 450 }}>
            <MyWork items={galleryItems} />
          </div> */}
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default EditComponent; 