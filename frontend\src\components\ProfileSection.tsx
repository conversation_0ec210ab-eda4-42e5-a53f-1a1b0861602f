import { Button } from './ui/buttons';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { API_BASE_URL } from '../config';
import Badge from './ui/Badge';
import Card from './ui/Card';
import { CardBody, CardTitle } from './ui/Card';
import Grid from './layout/Grid';
import {
  User,
  MapPin,
  Mail,
  Phone,
  DollarSign,
} from 'lucide-react';
import QuoteRequestModal from './QuoteRequestModal';

interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  is_professional: boolean;
}

// Interface pour les éléments du portfolio
interface PortfolioItem {
  id?: number;
  path?: string; 
  name?: string;
  type?: string;
  created_at?: string;
  // description?: string;
}

interface FreelanceProfile {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  phone: string;
  email?: string;
  address: string;
  city: string;
  country: string;
  skills: string[] | null;
  languages: string[] | null;
  availability_status: string | null;
  services_offered: string[];
  hourly_rate: string;
  completion_percentage: number;
  created_at: string;
  updated_at: string;
  avatar?: string;
  cover_photo?: string;
  profile_picture_path?: string;
  rating?: number;
  review_count?: number;
  bio?: string;
  title?: string;
  portfolio?: PortfolioItem[];
  user: User;
}

interface Achievement {
  id: number;
  freelance_profile_id: number;
  title: string;
  organization: string;
  date_obtained: string;
  description: string;
  file_path: string | null;
  achievement_url: string | null;
  created_at: string;
  updated_at: string;
}

type ProfileSectionProps = {
  id: string;
};

const ProfileSection = ({ id }: ProfileSectionProps) => {
  const [activeTab, setActiveTab] = useState('work');
  const [isQuoteModalOpen, setIsQuoteModalOpen] = useState(false);
  const navigate = useNavigate();
  
  const [professional, setProfessional] = useState<FreelanceProfile | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [loadingInvite, setLoadingInvitation] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const [offers, setOffers] = useState([]);
  const [selectedOfferId, setSelectedOfferId] = useState(null);
  const [showModal, setShowModal] = useState(false);

  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [projects, setProject] = useState<any | []>([]);
  const [services, setService] = useState<any | []>([]);

  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  console.log("Utisateur: ",user);

  // Fonction pour formater l'URL de l'image
  const getImageUrl = (imagePath: string | undefined, defaultImage: string = "https://randomuser.me/api/portraits/men/32.jpg") => {
    if (!imagePath) return defaultImage;
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/')) return `${API_BASE_URL}${imagePath}`;
    return `${API_BASE_URL}/${imagePath}`;
  };

  const getUrlProlfil = (path : string)  => {
      return path? `${API_BASE_URL}${path}`:'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
  };

  useEffect(() => {
    fetchProfessional();
    fetchClientPendingOffers();
    fetchAchievements();
    fetchServiceOffert();
  }, [id]);

  useEffect(() => {
    if(professional?.user_id != null){
      fetchServiceOffert();
    }
    
  }, [professional?.user_id]);

  const fetchServiceOffert = async () => {
        try {
          setLoading(true);
  
           const response = await fetch(`${API_BASE_URL}/api/professionals/${professional?.user_id}/service-offers`);
  
      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des réalisations');
      }
  
      const data = await response.json();
  
      console.log("data :", data);
  
      if (Array.isArray(data)) {
        const formatted = data.map((item) => ({
          id: item.id,
          title: item.title,
          description: item.description || 'Projet réalisé avec passion et expertise technique.',
          // image_url: item.files[0].path 
          //   ? `${API_BASE_URL}/storage/${item.files[0].path}`
          //   : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          
          image_url : Array.isArray(item.files) && item.files.length > 0
            ? `${API_BASE_URL}/storage/${item.files[0].path}`
            : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',     
          file_urls: Array.isArray(item.files)
            ? item.files.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
            : [],
          category: item.categories ? item.categories.join(" - ") : "",
          client_name: item.execution_time,
          professional_name: item.user.first_name+' '+item.user.last_name, // Si tu n’as pas cette info dans l’API
          professional_id: item.user.id || 1,
          date_create : item.created_at,
          price : item.price,
          user_id : item.user.id,
          avatar: item.user.professional_details.avatar? getUrlProlfil(String(item.user.professional_details.avatar)) :'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        }));
  
        console.log("Data Formater :", formatted);
        setService(formatted);
      } else {
        setService([]);
      }
          
          setLoading(false);
        } catch (error) {
          console.error("Erreur lors de la récupération des réalisations:", error);
          setError("Impossible de charger les réalisations. Veuillez réessayer plus tard.");
          setLoading(false);
        }
      };

  const fetchAchievements = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${API_BASE_URL}/api/professionals/${id}/achievements`);
        
        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des réalisations');
        }
        
        const data = await response.json();
        
        if (data.success && data.achievements) {
          setAchievements(data.achievements);

          const formatted = data.achievements.map((proj:any) => ({
            id: proj.id,
            title: proj.title,
            description: proj.description || 'Projet réalisé avec passion et expertise technique.',
            // image_url: proj.file_path 
            //   ? `${API_BASE_URL}/storage/${proj.file_path}`
            //   : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            image_url : Array.isArray(proj.files) && proj.files.length > 0
                    ? `${API_BASE_URL}/storage/${proj.files[0].path}`
                    : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            file_urls: Array.isArray(proj.files)
            ? proj.files.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
            : [],
            category: proj.organization || 'Projet',
            client_name: proj.organization || 'Organisation',
            professional_name: proj.professional.first_name+' '+proj.professional.last_name, // Si tu n’as pas cette info dans l’API
            professional_id: proj.professional_profile_id || 1,
            user_id : proj.professional.user_id,
            date_create : proj.date_obtained,
            avatar: proj.professional.avatar? getUrlProlfil(String(proj.professional.avatar)) :'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          }));

        console.log("Data Formater :", formatted);
        setProject(formatted);
        } else {
          setAchievements([]);
          setProject([]);
        }
        
        setLoading(false);
      } catch (error) {
        console.error("Erreur lors de la récupération des réalisations:", error);
        setError("Impossible de charger les réalisations. Veuillez réessayer plus tard.");
        setLoading(false);
      }
    };

  const handleContactProfessional = () => {
      if(token){
        if(user && !user.is_professional) {
          navigate(`/discussions/${professional?.user_id}`);
        }else{
          alert("Accès restreint : cette fonctionnalité est uniquement disponible pour les clients. Veuillez utiliser un compte client pour continuer.");
        }
        
      }else{
        navigate('/login');
      }
    // }
    

  };

  const handleBackToList = () => {
    // navigate('/lists-independants');
     navigate(-1);
  };

  const fetchProfessional = async () => {
        setLoading(true);
        try {
          // Essayer d'abord l'endpoint professionals
          let response = await fetch(`${API_BASE_URL}/api/professionals/${id}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json',
            },
          });
  
          // Si l'endpoint professionals échoue, essayer l'endpoint users
          if (!response.ok) {
            console.log('Endpoint professionals a échoué, essai de l\'endpoint users');
            response = await fetch(`${API_BASE_URL}/api/users/${id}`, {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json',
              },
            });
          }
  
          if (!response.ok) {
            throw new Error('Impossible de récupérer les détails du professionnel');
          }
  
          const data = await response.json();
          console.log('Données du professionnel récupérées:', data);
  
          // Traiter les données selon le format
          if (data.professional) {
            // Traiter les skills qui peuvent être une chaîne JSON ou un tableau
            let skills = [];
            if (data.professional.skills) {
              if (Array.isArray(data.professional.skills)) {
                skills = data.professional.skills;
              } else if (typeof data.professional.skills === 'string') {
                try {
                  skills = JSON.parse(data.professional.skills);
                } catch (e) {
                  skills = [data.professional.skills]; // Si ce n'est pas un JSON valide, le traiter comme une chaîne simple
                }
              }
            }
  
            // Traiter le portfolio qui peut être une chaîne JSON ou un tableau
            let portfolio = [];
            if (data.professional.portfolio) {
              if (Array.isArray(data.professional.portfolio)) {
                portfolio = data.professional.portfolio;
              } else if (typeof data.professional.portfolio === 'string') {
                try {
                  portfolio = JSON.parse(data.professional.portfolio);
                } catch (e) {
                  portfolio = []; // Si ce n'est pas un JSON valide, utiliser un tableau vide
                }
              }
            }
  
            // Mettre à jour les données du professionnel
            setProfessional({
              ...data.professional,
              skills: skills,
              portfolio: portfolio.length > 0 ? portfolio : [
                { title: 'Projet 3D 1', image: 'https://picsum.photos/seed/101/300/200', description: 'Modélisation 3D pour un jeu vidéo' },
                { title: 'Animation 3D', image: 'https://picsum.photos/seed/102/300/200', description: 'Animation de personnage' },
                { title: 'Rendu architectural', image: 'https://picsum.photos/seed/103/300/200', description: 'Visualisation architecturale' },
              ]
            });
          } else if (data.user && data.profile_data) {
            // Traiter le portfolio qui peut être une chaîne JSON ou un tableau
            let portfolio = [];
            if (data.profile_data.portfolio) {
              if (Array.isArray(data.profile_data.portfolio)) {
                portfolio = data.profile_data.portfolio;
              } else if (typeof data.profile_data.portfolio === 'string') {
                try {
                  portfolio = JSON.parse(data.profile_data.portfolio);
                } catch (e) {
                  portfolio = []; // Si ce n'est pas un JSON valide, utiliser un tableau vide
                }
              }
            }
  
            setProfessional({
              ...data.profile_data,
              user: data.user,
              portfolio: portfolio.length > 0 ? portfolio : [
                { title: 'Projet 3D 1', image: 'https://picsum.photos/seed/101/300/200', description: 'Modélisation 3D pour un jeu vidéo' },
                { title: 'Animation 3D', image: 'https://picsum.photos/seed/102/300/200', description: 'Animation de personnage' },
                { title: 'Rendu architectural', image: 'https://picsum.photos/seed/103/300/200', description: 'Visualisation architecturale' },
              ]
            });
          } else if (data.user) {
            setProfessional({
              id: data.user.id,
              user_id: data.user.id,
              first_name: data.user.first_name,
              last_name: data.user.last_name,
              phone: '',
              address: '',
              city: data.user.city || 'Paris',
              country: data.user.country || 'France',
              skills: data.user.skills || ['Animation 3D', 'Modélisation 3D', 'Rigging'],
              languages: ['Français', 'Anglais'],
              availability_status: data.user.availability_status || 'available',
              services_offered: ['Animation 3D', 'Modélisation 3D', 'Rigging'],
              hourly_rate: data.user.hourly_rate || '45',
              completion_percentage: data.completion_percentage || 100,
              created_at: data.user.created_at,
              updated_at: data.user.updated_at,
              avatar: data.user.avatar,
              profile_picture_path: data.user.profile_picture_path,
              rating: data.user.rating || 4.8,
              review_count: data.user.review_count || 27,
              bio: data.user.bio || 'Artiste 3D passionné avec plus de 5 ans d\'expérience dans la création de modèles 3D et d\'animations pour des jeux vidéo et des films.',
              title: data.user.title || 'Artiste 3D',
              portfolio: data.user.portfolio || [
                { title: 'Projet 3D 1', image: 'https://picsum.photos/seed/101/300/200', description: 'Modélisation 3D pour un jeu vidéo' },
                { title: 'Animation 3D', image: 'https://picsum.photos/seed/102/300/200', description: 'Animation de personnage' },
                { title: 'Rendu architectural', image: 'https://picsum.photos/seed/103/300/200', description: 'Visualisation architecturale' },
              ],
              user: data.user,
            });
          } else {
            // Utiliser des données de démonstration
            setProfessional({
              id: parseInt(id || '1'),
              user_id: parseInt(id || '1'),
              first_name: 'Thomas',
              last_name: 'Martin',
              phone: '+33 6 12 34 56 78',
              address: '123 Rue de la Création',
              city: 'Paris',
              country: 'France',
              skills: ['Animation 3D', 'Modélisation 3D', 'Rigging', 'Texturing', 'Rendu 3D'],
              languages: ['Français', 'Anglais'],
              availability_status: 'available',
              services_offered: ['Animation 3D', 'Modélisation 3D', 'Rigging'],
              hourly_rate: '45',
              completion_percentage: 100,
              created_at: '2023-01-01',
              updated_at: '2023-01-01',
              avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
              profile_picture_path: 'https://images.unsplash.com/photo-1717092068554-675b396555be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fDNkJTIwYXJ0aXN0JTIwYmxlbmRlcnxlbnwwfHwwfHx8MA%3D%3D',
              rating: 4.8,
              review_count: 27,
              bio: 'Artiste 3D passionné avec plus de 5 ans d\'expérience dans la création de modèles 3D et d\'animations pour des jeux vidéo et des films.',
              title: 'Artiste 3D',
              user: {
                id: parseInt(id || '1'),
                first_name: 'Thomas',
                last_name: 'Martin',
                email: '<EMAIL>',
                is_professional: true,
              },
            });
          }
        } catch (err) {
          console.error('Erreur lors de la récupération du professionnel:', err);
          if (err instanceof Error) {
            setError(err.message);
          } else {
            setError('Une erreur inconnue est survenue');
          }
  
          // Utiliser des données de démonstration en cas d'erreur
          setProfessional({
            id: parseInt(id || '1'),
            user_id: parseInt(id || '1'),
            first_name: 'Thomas',
            last_name: 'Martin',
            phone: '+33 6 12 34 56 78',
            address: '123 Rue de la Création',
            city: 'Paris',
            country: 'France',
            skills: ['Animation 3D', 'Modélisation 3D', 'Rigging', 'Texturing', 'Rendu 3D'],
            languages: ['Français', 'Anglais'],
            availability_status: 'available',
            services_offered: ['Animation 3D', 'Modélisation 3D', 'Rigging'],
            hourly_rate: '45',
            completion_percentage: 100,
            created_at: '2023-01-01',
            updated_at: '2023-01-01',
            avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
            profile_picture_path: 'https://images.unsplash.com/photo-1717092068554-675b396555be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fDNkJTIwYXJ0aXN0JTIwYmxlbmRlcnxlbnwwfHwwfHx8MA%3D%3D',
            rating: 4.8,
            review_count: 27,
            portfolio: [
              { name: 'Projet 3D 1', path: 'https://picsum.photos/seed/101/300/200', type: 'Modélisation 3D pour un jeu vidéo' },
              { name: 'Animation 3D', path: 'https://picsum.photos/seed/102/300/200', type: 'Animation de personnage' },
              { name: 'Rendu architectural', path: 'https://picsum.photos/seed/103/300/200', type: 'Visualisation architecturale' },
            ],
            bio: 'Artiste 3D passionné avec plus de 5 ans d\'expérience dans la création de modèles 3D et d\'animations pour des jeux vidéo et des films.',
            title: 'Artiste 3D',
            user: {
              id: parseInt(id || '1'),
              first_name: 'Thomas',
              last_name: 'Martin',
              email: '<EMAIL>',
              is_professional: true,
            },
          });
        } finally {
          setLoading(false);
        }
      };
  
      const fetchClientPendingOffers = async () => {
        if (!token) return;
        setLoading(true);
        try {
          const response = await fetch(`${API_BASE_URL}/api/client/open-offers/pending`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Accept': 'application/json',
            },
          });
  
          if (!response.ok) {
            throw new Error(`Erreur ${response.status}`);
          }
  
          const data = await response.json();
          console.log("Mes Offre : ", data);
          setOffers(data.offers);
          return data.offers;
        } catch (error) {
          console.error('Erreur lors de la récupération des offres client :', error);
          return [];
        }finally{
          setLoading(false);
        }
      };

  
  if (loading) {
      return (
        <div className="min-h-screen bg-white flex justify-center items-center">
          <div className="text-center p-6">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-neutral-700">Chargement des détails du professionnel...</p>
          </div>
        </div>
      );
    }
  
    if (error && !professional) {
      return (
        <div className="min-h-screen bg-white flex justify-center items-center">
          <div className="text-center p-6 bg-red-50 rounded-lg border border-red-200 max-w-md">
            {/* <h2 className="text-xl font-semibold text-red-700 mb-2">Erreur</h2>: */}
            {/* <p className="text-neutral-700">{error}</p> */}
            <p className="text-neutral-700">Chargement encores</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={handleBackToList}
            >
              Retour à la liste des professionnels
            </Button>
          </div>
        </div>
      );
    }


  const renderContent = () => {
    switch (activeTab) {
      case 'offers':
        return (
          <div className="animate-fade-in">
            {services.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-20 text-center animate-fade-in">
                  <img
                    src="https://cdn-icons-png.flaticon.com/512/4076/4076508.png"
                    alt="No services"
                    className="w-24 h-24 mb-6 opacity-80"
                  />
                  <h2 className="text-2xl font-semibold text-gray-700">No service offer found</h2>
                  <p className="text-gray-500 mt-2 max-w-md">
                    We couldn't find any service offer at the moment. Please check back later or try a different search.
                  </p>
                  <button
                    onClick={() => navigate("/")}
                    className="mt-6 px-6 py-3 bg-green-600 text-white rounded-full hover:bg-green-700 transition-all shadow-lg"
                  >
                    Back to homepage
                  </button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {services.map((service: any) => (
                    <div
                      key={service.id}
                      className="group cursor-pointer"
                      onClick={() => {
                        navigate("/details-search", {
                          state: { service },
                        });
                      }}
                    >
                      <div
                        className="rounded-2xl aspect-[4/3] flex items-center justify-center overflow-hidden relative hover:transform hover:scale-105 transition-all duration-300 bg-cover bg-center"
                        style={{
                          backgroundImage: `url(${getImageUrl(
                            service.image_url,
                            `https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop`
                          )})`,
                        }}
                      >
                        <div className="absolute inset-0 bg-gradient-to-br from-green-800 to-green-900 opacity-20"></div>
                        <div className="relative z-10 text-center">
                          <h3 className="text-2xl font-light text-white tracking-wider">{service.title}</h3>
                          <p className="text-green-200 text-sm mt-2">À partir de €{service.price}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            {/* <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {services.map((service:any) => (
                <div 
                  key={service.id}
                  className="group cursor-pointer"
                  onClick={() => {
                    navigate('/details-search', {
                      state: { service }
                    });
                  }}
                >
                  <div 
                    className="rounded-2xl aspect-[4/3] flex items-center justify-center overflow-hidden relative hover:transform hover:scale-105 transition-all duration-300 bg-cover bg-center"
                    style={{ backgroundImage: `url(${getImageUrl(service.image_url, `https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D`)})` }}
                    >
                    <div className="absolute inset-0 bg-gradient-to-br from-green-800 to-green-900 opacity-20"></div>
                    <div className="relative z-10 text-center">
                      <h3 className="text-2xl font-light text-white tracking-wider">{service.title}</h3>
                      <p className="text-green-200 text-sm mt-2">À partir de €{service.price}</p>
                    </div>
                  </div>
                </div>
              ))}
              
            </div> */}
          </div>
        );
      case 'about':
        return (
          <div className="animate-fade-in">
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">About me</h3>
              <div className="prose prose-lg text-gray-700 space-y-4">
                <p>
                  {professional?.bio||'Aucune biographie disponible.' }
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Skills</h4>
                    <ul className="space-y-2 text-gray-600">
                      {professional?.skills?.map((skill, index) => (
                        <li>• {skill}</li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">languages</h4>
                    <ul className="space-y-2 text-gray-600">
                      {professional?.languages?.map((language, index) => (
                        <li>• {language}</li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3">Contact</h4>
                    <ul className="space-y-2 text-gray-600">
                      <li className="flex items-center">
                        <MapPin className="h-5 w-5 text-neutral-500 mr-2" />
                        <span>{professional?.city}, {professional?.country}</span>
                      </li>
                      <li className="flex items-center"> 
                        <Mail className="h-5 w-5 text-neutral-500 mr-2" />
                        <span>{professional?.email}</span> </li>
                      <li className="flex items-center">
                        <Phone className="h-5 w-5 text-neutral-500 mr-2" />
                        <span>{professional?.phone}</span>
                      </li>
                      <li className="flex items-center">
                        <DollarSign className="h-4 w-4 mr-1" />
                        <span>{professional?.hourly_rate || '45'}€/heure</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              
              <CardTitle className="text-xl mb-4 py-2">Portfolio</CardTitle>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {professional?.portfolio && Array.isArray(professional.portfolio) && professional.portfolio.length > 0 ? (
                  professional.portfolio.map((item, index) => (
                    <div className="group cursor-pointer">
                      <div
                        className="rounded-2xl aspect-[4/3] flex items-center justify-center overflow-hidden relative hover:transform hover:scale-105 transition-all duration-300 bg-cover bg-center"
                        style={{ backgroundImage: `url(${getImageUrl(item.path, `https://picsum.photos/seed/${professional?.id || 1}${index}/300/200`)})` }}
                      >
                        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 to-black opacity-20"></div>

                        <div className="absolute inset-0 opacity-20">
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-gray-700 to-transparent transform skew-y-12 animate-pulse"></div>
                        </div>

                        <div className="relative z-10 text-center">
                          {/* <div className="w-8 h-8 border-2 border-white rounded-full mx-auto mb-4 flex items-center justify-center">
                            <div className="w-3 h-3 bg-white rounded-full"></div>
                          </div> */}
                          <h3 className="text-2xl font-light text-white tracking-wider">{item.name}</h3>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  // Afficher des images de démonstration si aucun portfolio n'est disponible
                  [1, 2, 3, 4, 5, 6].map((item) => (
                    <div key={item} className="overflow-hidden rounded-lg border border-neutral-200">
                      <img
                        src={`https://picsum.photos/seed/${professional?.id || 1}${item}/300/200`}
                        alt={`Réalisation ${item}`}
                        className="w-full h-40 object-cover"
                      />
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        );
      default: // work
        return (
          <div className="animate-fade-in">
            {projects.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-20 text-center animate-fade-in">
              <img
                src="https://cdn-icons-png.flaticon.com/512/10437/10437090.png"
                alt="No projects found"
                className="w-24 h-24 mb-6 opacity-80"
              />
              <h2 className="text-2xl font-semibold text-gray-700">No projects found</h2>
              <p className="text-gray-500 mt-2 max-w-md">
                We couldn't find any projects to display. Please check back later or try a different search.
              </p>
              <button
                onClick={() => navigate("/")}
                className="mt-6 px-6 py-3 bg-gray-800 text-white rounded-full hover:bg-gray-900 transition-all shadow-lg"
              >
                Go back to homepage
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {projects.map((project: any) => (
                <div
                  key={project.id}
                  className="group cursor-pointer"
                  onClick={() => {
                    navigate("/details-search", {
                      state: { project },
                    });
                  }}
                >
                  <div
                    className="rounded-2xl aspect-[4/3] flex items-center justify-center overflow-hidden relative hover:transform hover:scale-105 transition-all duration-300 bg-cover bg-center"
                    style={{
                      backgroundImage: `url(${getImageUrl(
                        project.image_url,
                        `https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop`
                      )})`,
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-gray-900 to-black opacity-50"></div>

                    <div className="absolute inset-0 opacity-20">
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-gray-700 to-transparent transform skew-y-12 animate-pulse"></div>
                    </div>

                    <div className="relative z-10 text-center">
                      <h3 className="text-2xl font-light text-white tracking-wider">
                        {project.title}
                      </h3>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

            {/* <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {projects.map((project:any) => (
                <div 
                  key={project.id}
                  className="group cursor-pointer"
                  onClick={() => {
                    navigate('/details-search', {
                      state: { project }
                    });
                  }}
                >
                  <div
                    className="rounded-2xl aspect-[4/3] flex items-center justify-center overflow-hidden relative hover:transform hover:scale-105 transition-all duration-300 bg-cover bg-center"
                    style={{ backgroundImage: `url(${getImageUrl(project.image_url, `https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D`)})` }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-gray-900 to-black opacity-50"></div>

                    <div className="absolute inset-0 opacity-20">
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-gray-700 to-transparent transform skew-y-12 animate-pulse"></div>
                    </div>

                    <div className="relative z-10 text-center">
                      <h3 className="text-2xl font-light text-white tracking-wider">{project.title}</h3>
                    </div>
                  </div>
                </div>
              ))}
            </div> */}
            
            {/* Load More Button */}
            <div className="flex justify-center mt-16">
              <button className="w-12 h-12 bg-gray-300 hover:bg-gray-400 rounded-full flex items-center justify-center transition-all duration-300 hover:transform hover:scale-110">
                <span className="text-gray-600 text-xl">↓</span>
              </button>
            </div>
          </div>
        );
    }
  };

  return (
    <section className="bg-gray-50 pt-20 pb-16 px-4">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col lg:flex-row items-start justify-between gap-8">
          <div className="flex-1">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-16 h-16 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden">
                <img 
                  src={getImageUrl(professional?.avatar)}
                  alt={`${professional?.first_name} ${professional?.last_name}`}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                      e.currentTarget.onerror = null; // empêche les boucles infinies
                      e.currentTarget.src = 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; // chemin de l'image par défaut
                    }}
                />
              </div>
              
            </div>
            
            <div className="mb-6">
              <div>
                <h1 className="text-2xl font-bold text-black">{`${professional?.first_name} ${professional?.last_name}`}</h1>
              </div>
              <h2 className="text-4xl lg:text-5xl font-bold text-black leading-tight mb-2">
                {professional?.title || 'Artiste 3D'}
                {/* Brands, Icons, Logotypes. */}
              </h2>
              <h3 className="text-4xl lg:text-5xl font-bold text-black leading-tight mb-4">
                Let's talk! 
                {/* <span className="text-3xl">↘</span> */}
              </h3>
            </div>
            
            <div className="flex items-center gap-6 text-sm text-gray-600 mb-6">
              <span>{professional?.rating || 4.8} rating</span>
              <span>{professional?.review_count || 27} reviews</span>
              <span>3,573 likes</span>
            </div>
            
            <div className="flex items-center gap-4">
              <Button 
                onClick={() => setIsQuoteModalOpen(true)}
                className="bg-black text-white hover:bg-gray-800 rounded-full px-6 py-2 transition-all duration-300 hover:transform hover:scale-105"
              >
                Let's make a deal
              </Button>
              <Button variant="ghost" className="text-black hover:bg-gray-100 transition-all duration-300">
                ...
              </Button>
            </div>
            {/* <div className="flex items-center gap-4">
              <Button className="bg-black text-white hover:bg-gray-800 rounded-full px-6 py-2 transition-all duration-300 hover:transform hover:scale-105">
                Let's make a deal
              </Button>
              <Button variant="ghost" className="text-black hover:bg-gray-100 transition-all duration-300">
                ...
              </Button>
            </div> */}
          </div>
          
          <div className="lg:w-96">
            <div className="bg-black rounded-2xl p-8 text-white relative overflow-hidden h-80">
              <div className="absolute top-4 right-4 w-16 h-16 border-2 border-white rounded-full flex items-center justify-center">
                <div className="w-8 h-8 bg-white rounded-full"></div>
              </div>
              {/* <div className="mt-12">
                <h3 className="text-2xl font-bold text-red-500 mb-2">{`${professional?.first_name} ${professional?.last_name}`}</h3>
                <p className="text-gray-400 text-sm">
                  {professional?.skills?.map((skill, index) => (
                    <Badge key={index} variant="neutral">{skill}</Badge> 
                      ))}
                </p>
              </div> */}
              <div className="absolute bottom-8 left-8 right-8">
                <h3 className="text-2xl font-bold text-red-500 mb-2">{`${professional?.first_name} ${professional?.last_name}`}</h3>
                <p className="text-gray-400 text-sm mb-4">
                  {professional?.skills?.map((skill, index) => (
                    <Badge key={index} variant="neutral">{skill}</Badge> 
                      ))}
                </p>
                {/* <div className="text-gray-400 text-xs mb-2">Disponible pour nouveaux projets</div> */}
                <div className="flex items-center gap-2">
                  {/* <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div> */}
                  <Badge
                        variant={professional?.availability_status === 'available' ? 'success' : professional?.availability_status === 'busy' ? 'warning' : 'error'}
                        size="md"
                        className="mb-2"
                      >
                        {professional?.availability_status === 'available' ? 'Disponible' : professional?.availability_status === 'busy' ? 'Occupé' : 'Indisponible'}
                  </Badge>
                  {/* <span className="text-green-400 text-sm">En ligne</span> */}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-12 border-b border-gray-300">
          <nav className="flex space-x-8">
            <button 
              onClick={() => setActiveTab('offers')}
              className={`pb-4 font-medium transition-all duration-300 ${
                activeTab === 'offers' 
                  ? 'text-black border-b-2 border-black' 
                  : 'text-gray-400 hover:text-black'
              }`}
            >
              My offers
            </button>
            <button 
              onClick={() => setActiveTab('work')}
              className={`pb-4 font-medium transition-all duration-300 ${
                activeTab === 'work' 
                  ? 'text-black border-b-2 border-black' 
                  : 'text-gray-400 hover:text-black'
              }`}
            >
              My work
            </button>
            <button 
              onClick={() => setActiveTab('about')}
              className={`pb-4 font-medium transition-all duration-300 ${
                activeTab === 'about' 
                  ? 'text-black border-b-2 border-black' 
                  : 'text-gray-400 hover:text-black'
              }`}
            >
              About me
            </button>
          </nav>
        </div>
        
        <div className="mt-12">
          {renderContent()}
        </div>
      </div>

      <QuoteRequestModal 
        token={token}
        user={user}
        pro={professional}
        open={isQuoteModalOpen}
        onOpenChange={setIsQuoteModalOpen}
      />
    </section>
  );
};

export default ProfileSection;