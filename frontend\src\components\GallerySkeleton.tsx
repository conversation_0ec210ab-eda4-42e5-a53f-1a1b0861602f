import React from 'react';

const skeletonItems = Array.from({ length: 8 }); // 8 skeleton cards

const GallerySkeleton: React.FC = () => (
  <div className="max-w-[1357px] mx-auto">
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 sm:gap-8 px-2 sm:px-0 justify-items-center">
      {skeletonItems.map((_, idx) => (
        <div
          key={idx}
          className="w-[90vw] max-w-[315px] bg-white rounded-lg overflow-hidden m-auto relative shadow-none flex flex-col animate-pulse"
        >
          <div className="bg-gray-200 w-full h-[220px] rounded-lg" />
          <div className="flex items-center gap-2.5 px-0 pt-[18px] pb-2.5 min-h-[60px]">
            <div className="w-8 h-8 rounded-full bg-gray-200" />
            <div className="h-4 w-20 bg-gray-200 rounded" />
            <div className="h-4 w-10 bg-gray-200 rounded ml-2" />
          </div>
          <div className="flex items-center gap-4 text-[#888] px-0 pb-2">
            <div className="h-4 w-8 bg-gray-200 rounded" />
            <div className="h-4 w-8 bg-gray-200 rounded" />
          </div>
        </div>
      ))}
    </div>
  </div>
);

export default GallerySkeleton; 