import Header from './Header';
import Footer from './Footer';
import <PERSON> from './Hero';
import PortfolioPage from './PortfolioPage';
import SignupButton from './SignupButton';
import React, { useState, useEffect } from 'react';
import { API_BASE_URL } from './../config';
import { exploreService, Professional, Project, Category, FilterOptions } from './../services/exploreService';

// Interfaces définies dans exploreService.ts
interface ProfessionalProfile {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  email: string;
  avatar: string;
  portfolio_items: any | null;
  phone: string;
  address: string;
  city: string;
  country: string;
  bio: string;
  title: string | null;
  expertise: string | null;
  completion_percentage: number;
  profession: string;
  years_of_experience: number;
  hourly_rate: string;
  description: string | null;
  availability_status: string;
  estimated_response_time: string | null;
  rating: string;
  skills: string[];
  languages: string[];
  services_offered: string[];
  portfolio: Array<{
    id: string;
    path: string;
    name: string;
    type: string;
    created_at: string;
  }>;
  social_links: any[];
  created_at: string;
  updated_at: string;
}

interface AchievementFile {
  path: string;
  original_name: string;
  mime_type: string;
  size: number;
}

interface Achievement {
  id: number;
  title: string;
  description: string;
  file_path: string;
  files: AchievementFile[];
  organization: string;
  achievement_url: string;
  date_obtained: string; // Nouvelle rubrique
  created_at: string;
  updated_at: string;
  professional_profile_id: number;
  professional_profile: ProfessionalProfile; // Nouvelle rubrique ajoutée
}

interface AchievementResponse {
  success: boolean;
  achievements: Achievement[];
}

const HomePage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [professionals, setProfessionals] = useState<Professional[]>([]);
  const [filteredProfessionals, setFilteredProfessionals] = useState<Professional[]>([]);
  const [filteredPros, setFilteredPros] = useState<Professional[]>([]);

  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  const getUrlProlfil = (path : string)  => {
        return path? `${API_BASE_URL}${path}`:'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
  };

  const fetchProfessionals = async () => {
      const response = await exploreService.getProfessionals();
      return response.professionals.map(pro => ({
        id: pro.id,
        id_user: pro.user_id,
        first_name: pro.first_name,
        last_name: pro.last_name,
        title: pro.title || 'Artiste 3D',
        skills: pro.skills || ['Modélisation', 'Blender', 'Maya'],
        rating: pro.rating || 4.5,
        review_count: pro.review_count || 10,
        profile_picture_path: pro.profile_picture_path || 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        city: pro.city || 'Paris',
        country: pro.country || 'France',
        availability_status: (pro.availability_status || 'available') as 'available' | 'busy' | 'unavailable',
        service_offer: (pro.service_offer || []).map((proj: any) => ({
          avatar: pro.profile_picture_path? getUrlProlfil(String(pro.profile_picture_path)) :'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          id: proj.id,
          title: proj.title,
          description: proj.description || 'Projet réalisé avec passion et expertise technique.',
          // image_url: proj.files?.[0]?.path
          //   ? `${API_BASE_URL}/storage/${proj.files[0].path}`
          //   : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          
          image_url : Array.isArray(proj.files) && proj.files.length > 0
          ? `${API_BASE_URL}/storage/${proj.files[0].path}`
          : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',     
          file_urls: Array.isArray(proj.files)
          ? proj.files.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
          : [],
          category: proj.categories ? proj.categories.join(" - ") : "",
          client_name: proj.execution_time,
          date_create: proj.created_at,
          price: proj.price,
          user_id: pro.user_id,
          professional_name: `${pro?.first_name || ''} ${pro?.last_name || ''}`.trim()
        })),
        achievements: (pro.achievements || []).map((ach: any) => ({
          avatar: pro.profile_picture_path? getUrlProlfil(String(pro.profile_picture_path)) :'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          id: ach.id,
          title: ach.title,
          description: ach.description || 'Réalisé avec expertise.',
          image_url : Array.isArray(ach.files) && ach.files.length > 0
          ? `${API_BASE_URL}/storage/${ach.files[0].path}`
          : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
  
          // image_url: ach.file_path
          //   ? `${API_BASE_URL}/storage/${ach.file_path}`
          //   : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          
            file_urls: Array.isArray(ach.files)
          ? ach.files.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
          : [],
          category: ach.organization,
          client_name: ach.organization,
          date_create: ach.date_obtained,
          price: '',
          user_id: pro.user_id,
          professional_name: `${pro?.first_name || ''} ${pro?.last_name || ''}`.trim()
        })),
      }));
    };

  const fetchData = async () => {
    setLoading(true);

    try {
      const professionals = await fetchProfessionals();

      if (professionals.length > 0) {
        setProfessionals(professionals);
        setFilteredProfessionals(professionals);
        setFilteredPros(professionals);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      // Ici, tu peux insérer une logique de fallback si tu veux afficher des données fictives
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  },[]);

  const handleSearch = (query: string, type: string) => {
    if (!query) {
      setFilteredPros(professionals);
      return;
    }

    const result = professionals.filter((pro) => {
      if (type === "3D Artiste" || type === "Search By") {
        return (
          pro.first_name.toLowerCase().includes(query) ||
          pro.last_name.toLowerCase().includes(query)
        );
      } else if (type === "Services") {
        return pro.service_offer?.some((s: any) =>
          s.title?.toLowerCase().includes(query)
        );
      }
      return false;
    });

    setFilteredPros(result);
  };

  if (loading) {
      return (
        <div className="min-h-screen bg-white">
          <Header />
          <div className="flex justify-center items-center h-96">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
          </div>
          <Footer />
        </div>
      );
    }
  return (
    <>
      <Header />
      <Hero onSearch={handleSearch}/>
      <PortfolioPage 
        pros={filteredPros}
      />
      {/* Afficher le bouton d'inscription uniquement si l'utilisateur n'est pas connecté */}
      {(!token || !user) && <SignupButton />}
      <Footer />
    </>
  );
};

export default React.memo(HomePage);
