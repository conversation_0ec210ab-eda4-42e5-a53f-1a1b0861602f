# Project Workflow

## Vue d'ensemble

Ce document décrit le workflow complet des projets dans l'application Hi3D. Il existe deux types de projets distincts :

1. **Projects** : Projets liés aux expériences professionnelles (portfolio)
2. **DashboardProjects** : Projets de tableau de bord pour la gestion client

## Architecture

### Modèles principaux

#### Project (Portfolio)
- **Project** : Projets associés aux expériences professionnelles
- **Experience** : Expériences professionnelles des freelances
- **ProfessionalProfile** : Profil professionnel du freelance

#### DashboardProject (Gestion client)
- **DashboardProject** : Projets de gestion pour les clients
- **User** : Utilisateur propriétaire du projet

### Contrôleurs
- **ProjectController** : Gestion des projets portfolio
- **DashboardProjectController** : Gestion des projets dashboard

## Workflow des Projects (Portfolio)

### Architecture relationnelle
```
User → ProfessionalProfile → Experience → Project
```

### Workflow de création d'un projet portfolio

```mermaid
graph TD
    A[Utilisateur avec profil pro] --> B[Sélection expérience]
    B --> C[Formulaire création projet]
    C --> D[Validation données]
    D --> E{Validation OK?}
    E -->|Non| F[Retour erreurs]
    E -->|Oui| G[Upload image optionnel]
    G --> H[Création en base]
    H --> I[Association à l'expérience]
    I --> J[Retour succès]
    
    F --> C
```

#### Étapes détaillées

1. **Prérequis**
   - Utilisateur authentifié
   - Profil professionnel existant
   - Expérience professionnelle créée

2. **Validation des données** (ProjectRequest)
   - `name` : requis, string, max 255 caractères
   - `description` : optionnel, texte
   - `image` : optionnel, fichier image (jpeg,png,jpg,gif,svg, max 2MB)
   - `project_url` : optionnel, URL valide, max 255 caractères

3. **Gestion des fichiers**
   - Stockage dans `project_images` (disque public)
   - Formats acceptés : jpeg, png, jpg, gif, svg
   - Taille maximale : 2048 Ko

4. **Création en base**
   - Attribution automatique de l'`experience_id`
   - Sauvegarde du chemin de l'image

### Workflow de mise à jour

```mermaid
graph TD
    A[Demande mise à jour] --> B[Validation données]
    B --> C[Gestion nouvelle image]
    C --> D[Suppression ancienne image]
    D --> E[Upload nouvelle image]
    E --> F[Mise à jour en base]
    F --> G[Retour succès]
```

### Workflow de suppression

```mermaid
graph TD
    A[Demande suppression] --> B[Vérification existence]
    B --> C[Suppression image associée]
    C --> D[Suppression en base]
    D --> E[Retour succès]
```

## Workflow des DashboardProjects (Gestion client)

### Workflow de création

```mermaid
graph TD
    A[Client connecté] --> B[Formulaire projet]
    B --> C[Validation données]
    C --> D{Validation OK?}
    D -->|Non| E[Retour erreurs]
    D -->|Oui| F[Upload attachments]
    F --> G[Création en base]
    G --> H[Statut 'open' par défaut]
    H --> I[Retour succès]
    
    E --> B
```

#### Validation des données

1. **Champs requis**
   - `title` : requis, string, max 255 caractères
   - `description` : requis, texte
   - `category` : requis, string
   - `budget` : requis, string
   - `deadline` : requis, date

2. **Champs optionnels**
   - `skills` : array de strings
   - `attachments` : array de fichiers (max 10MB chacun)

3. **Gestion des fichiers**
   - Types acceptés : jpeg, png, jpg, gif, pdf, doc, docx
   - Taille maximale : 10240 Ko par fichier
   - Stockage dans `dashboard_project_attachments`

### Statuts des DashboardProjects

- **draft** : Brouillon (non publié)
- **open** : Ouvert aux candidatures
- **in_progress** : En cours de réalisation
- **completed** : Terminé
- **cancelled** : Annulé

### Workflow de mise à jour des statuts

```mermaid
graph TD
    A[draft] --> B[open]
    B --> C[in_progress]
    C --> D[completed]
    B --> E[cancelled]
    C --> E
    A --> E
```

## API Endpoints

### Projects (Portfolio)
- `POST /api/experiences/{experience}/projects` : Création
- `GET /api/projects/{project}` : Consultation
- `PUT /api/projects/{project}` : Mise à jour
- `DELETE /api/projects/{project}` : Suppression

### DashboardProjects
- `GET /api/dashboard-projects` : Liste des projets utilisateur
- `POST /api/dashboard-projects` : Création
- `GET /api/dashboard-projects/{id}` : Consultation
- `PUT /api/dashboard-projects/{id}` : Mise à jour
- `DELETE /api/dashboard-projects/{id}` : Suppression

## Règles d'autorisation

### Projects (Portfolio)
- Pas de vérification explicite d'autorisation dans le contrôleur
- Sécurité basée sur l'association Experience → ProfessionalProfile → User

### DashboardProjects
- Vérification stricte : `user_id` doit correspondre à l'utilisateur connecté
- Filtrage automatique par `user_id` dans toutes les opérations

## Gestion des erreurs

### Erreurs courantes
- **422** : Erreurs de validation
- **404** : Projet/Expérience non trouvé(e)
- **403** : Non autorisé (pour DashboardProjects)
- **500** : Erreurs serveur (upload, base de données)

### Logging
- Logging détaillé pour les DashboardProjects
- Messages d'erreur contextualisés
- Traces complètes en cas d'exception

## Différences clés entre les deux types

| Aspect | Projects (Portfolio) | DashboardProjects |
|--------|---------------------|-------------------|
| **Propriétaire** | Freelances | Clients |
| **Relation** | Experience → Project | User → DashboardProject |
| **Fichiers** | Image unique | Multiples attachments |
| **Statuts** | Aucun | 5 statuts définis |
| **Validation** | ProjectRequest | Validation inline |
| **Sécurité** | Implicite via relations | Explicite par user_id |

## Bonnes pratiques

1. **Sécurité**
   - Validation stricte des fichiers uploadés
   - Vérification des autorisations
   - Sanitisation des inputs

2. **Performance**
   - Eager loading des relations
   - Optimisation des requêtes
   - Gestion efficace des fichiers

3. **UX**
   - Messages d'erreur clairs
   - Feedback pour uploads
   - Prévisualisation des images

## Évolutions futures

### Projects (Portfolio)
- Support de multiples images par projet
- Système de tags/catégories
- Intégration avec les service offers

### DashboardProjects
- Système de notifications pour changements de statut
- Collaboration multi-utilisateurs
- Intégration avec système de facturation
- Templates de projets prédéfinis
