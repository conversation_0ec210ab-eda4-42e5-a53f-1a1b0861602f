[2025-07-08 13:48:53] local.ERROR: SQLSTATE[2BP01]: Dependent objects still exist: 7 ERREUR:  n'a pas pu supprimer table dashboard_projects car d'autres objets en dépendent
DETAIL:  contrainte service_offers_associated_project_id_foreign sur table service_offers dépend de table dashboard_projects
HINT:  Utilisez DROP ... CASCADE pour supprimer aussi les objets dépendants. (Connection: pgsql, SQL: drop table if exists "dashboard_projects") {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2BP01): SQLSTATE[2BP01]: Dependent objects still exist: 7 ERREUR:  n'a pas pu supprimer table dashboard_projects car d'autres objets en dépendent
DETAIL:  contrainte service_offers_associated_project_id_foreign sur table service_offers dépend de table dashboard_projects
HINT:  Utilisez DROP ... CASCADE pour supprimer aussi les objets dépendants. (Connection: pgsql, SQL: drop table if exists \"dashboard_projects\") at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:829)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('drop table if e...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('drop table if e...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('drop table if e...')
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Schema\\Grammars\\PostgresGrammar))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(484): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->dropIfExists('dashboard_proje...')
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\database\\migrations\\2024_01_01_000014_create_dashboard_projects_table.php(34): Illuminate\\Support\\Facades\\Facade::__callStatic('dropIfExists', Array)
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->down()
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(30): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}(Object(Illuminate\\Database\\PostgresConnection))
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(426): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(393): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2024_01_01_0000...', Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(393): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_01_0000...', Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(312): Illuminate\\Database\\Migrations\\Migrator->runDown('C:\\\\Users\\\\<USER>\\\\...', Object(stdClass), false)
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(367): Illuminate\\Database\\Migrations\\Migrator->rollbackMigrations(Array, Array, Array)
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(343): Illuminate\\Database\\Migrations\\Migrator->resetMigrations(Array, Array, false)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(66): Illuminate\\Database\\Migrations\\Migrator->reset(Array, false)
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\ResetCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(58): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\ResetCommand->handle()
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate:reset', Array, Object(Illuminate\\Console\\OutputStyle))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(109): Illuminate\\Console\\Command->call('migrate:reset', Array)
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(55): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->runReset(NULL, Array)
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->handle()
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RefreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 {main}

[previous exception] [object] (PDOException(code: 2BP01): SQLSTATE[2BP01]: Dependent objects still exist: 7 ERREUR:  n'a pas pu supprimer table dashboard_projects car d'autres objets en dépendent
DETAIL:  contrainte service_offers_associated_project_id_foreign sur table service_offers dépend de table dashboard_projects
HINT:  Utilisez DROP ... CASCADE pour supprimer aussi les objets dépendants. at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:587)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(587): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('drop table if e...', Array)
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('drop table if e...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(576): Illuminate\\Database\\Connection->run('drop table if e...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(110): Illuminate\\Database\\Connection->statement('drop table if e...')
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(602): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Schema\\Grammars\\PostgresGrammar))
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(484): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->dropIfExists('dashboard_proje...')
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\database\\migrations\\2024_01_01_000014_create_dashboard_projects_table.php(34): Illuminate\\Support\\Facades\\Facade::__callStatic('dropIfExists', Array)
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(501): Illuminate\\Database\\Migrations\\Migration@anonymous->down()
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(418): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\PostgresConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(30): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}(Object(Illuminate\\Database\\PostgresConnection))
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(426): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(393): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(783): Illuminate\\Console\\View\\Components\\Task->render('2024_01_01_0000...', Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(393): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_01_0000...', Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(312): Illuminate\\Database\\Migrations\\Migrator->runDown('C:\\\\Users\\\\<USER>\\\\...', Object(stdClass), false)
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(367): Illuminate\\Database\\Migrations\\Migrator->rollbackMigrations(Array, Array, Array)
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(343): Illuminate\\Database\\Migrations\\Migrator->resetMigrations(Array, Array, false)
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(66): Illuminate\\Database\\Migrations\\Migrator->reset(Array, false)
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\ResetCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\ResetCommand.php(58): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\ResetCommand->handle()
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('migrate:reset', Array, Object(Illuminate\\Console\\OutputStyle))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(109): Illuminate\\Console\\Command->call('migrate:reset', Array)
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\RefreshCommand.php(55): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->runReset(NULL, Array)
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RefreshCommand->handle()
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#41 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RefreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\artisan(34): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 {main}
"} 
[2025-07-08 14:16:15] local.ERROR: Route [login] not defined. {"exception":"[object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [login] not defined. at C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('login', Array, true)
#1 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\app\\Http\\Middleware\\Authenticate.php(15): route('login')
#2 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(96): App\\Http\\Middleware\\Authenticate->redirectTo(Object(Illuminate\\Http\\Request))
#3 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(81): Illuminate\\Auth\\Middleware\\Authenticate->unauthenticated(Object(Illuminate\\Http\\Request), Array)
#4 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(55): Illuminate\\Auth\\Middleware\\Authenticate->authenticate(Object(Illuminate\\Http\\Request), Array)
#5 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'sanctum')
#6 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(24): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#15 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Documents\\MD\\wassim\\04Juiell\\hi3d\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#37 {main}
"} 
[2025-07-08 14:17:14] testing.INFO: setSkillsAttribute appelé avec: ["Python","Laravel","Git","CI\/CD","PHP","AWS","Django","React"] (type: array)  
[2025-07-08 14:17:14] testing.INFO: Skills encodé directement: ["Python","Laravel","Git","CI\/CD","PHP","AWS","Django","React"]  
[2025-07-08 14:17:14] testing.INFO: setLanguagesAttribute appelé avec: ["Anglais","Espagnol","Fran\u00e7ais"] (type: array)  
[2025-07-08 14:17:14] testing.INFO: Languages encodé directement: ["Anglais","Espagnol","Fran\u00e7ais"]  
[2025-07-08 14:17:14] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web","D\u00e9veloppement Mobile","Formation"] (type: array)  
[2025-07-08 14:17:14] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web","D\u00e9veloppement Mobile","Formation"]  
[2025-07-08 14:17:15] testing.INFO: setSkillsAttribute appelé avec: ["Agile","JavaScript","Vue.js","React","Python","Java","C#","PostgreSQL"] (type: array)  
[2025-07-08 14:17:15] testing.INFO: Skills encodé directement: ["Agile","JavaScript","Vue.js","React","Python","Java","C#","PostgreSQL"]  
[2025-07-08 14:17:15] testing.INFO: setLanguagesAttribute appelé avec: ["Fran\u00e7ais","Espagnol","Anglais"] (type: array)  
[2025-07-08 14:17:15] testing.INFO: Languages encodé directement: ["Fran\u00e7ais","Espagnol","Anglais"]  
[2025-07-08 14:17:15] testing.INFO: setServicesOfferedAttribute appelé avec: ["Formation","Maintenance"] (type: array)  
[2025-07-08 14:17:15] testing.INFO: Services_offered encodé directement: ["Formation","Maintenance"]  
[2025-07-08 14:17:16] testing.INFO: setSkillsAttribute appelé avec: ["MySQL","Python","Kubernetes","AWS","PostgreSQL","Agile","Git"] (type: array)  
[2025-07-08 14:17:16] testing.INFO: Skills encodé directement: ["MySQL","Python","Kubernetes","AWS","PostgreSQL","Agile","Git"]  
[2025-07-08 14:17:16] testing.INFO: setLanguagesAttribute appelé avec: ["Allemand"] (type: array)  
[2025-07-08 14:17:16] testing.INFO: Languages encodé directement: ["Allemand"]  
[2025-07-08 14:17:16] testing.INFO: setServicesOfferedAttribute appelé avec: ["Consulting","Maintenance"] (type: array)  
[2025-07-08 14:17:16] testing.INFO: Services_offered encodé directement: ["Consulting","Maintenance"]  
[2025-07-08 14:17:17] testing.INFO: setSkillsAttribute appelé avec: ["Django","React","Laravel","PHP",".NET","PostgreSQL","Docker"] (type: array)  
[2025-07-08 14:17:17] testing.INFO: Skills encodé directement: ["Django","React","Laravel","PHP",".NET","PostgreSQL","Docker"]  
[2025-07-08 14:17:17] testing.INFO: setLanguagesAttribute appelé avec: ["Allemand","Espagnol"] (type: array)  
[2025-07-08 14:17:17] testing.INFO: Languages encodé directement: ["Allemand","Espagnol"]  
[2025-07-08 14:17:17] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: array)  
[2025-07-08 14:17:17] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web"]  
[2025-07-08 14:17:17] testing.INFO: setSkillsAttribute appelé avec: ["CI\/CD","Kubernetes","MySQL"] (type: array)  
[2025-07-08 14:17:17] testing.INFO: Skills encodé directement: ["CI\/CD","Kubernetes","MySQL"]  
[2025-07-08 14:17:17] testing.INFO: setLanguagesAttribute appelé avec: ["Espagnol"] (type: array)  
[2025-07-08 14:17:17] testing.INFO: Languages encodé directement: ["Espagnol"]  
[2025-07-08 14:17:17] testing.INFO: setServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Web"] (type: array)  
[2025-07-08 14:17:17] testing.INFO: Services_offered encodé directement: ["Formation","D\u00e9veloppement Web"]  
[2025-07-08 14:18:19] testing.INFO: setSkillsAttribute appelé avec: ["AWS","Agile","PostgreSQL","MySQL"] (type: array)  
[2025-07-08 14:18:19] testing.INFO: Skills encodé directement: ["AWS","Agile","PostgreSQL","MySQL"]  
[2025-07-08 14:18:19] testing.INFO: setLanguagesAttribute appelé avec: ["Espagnol","Allemand","Fran\u00e7ais"] (type: array)  
[2025-07-08 14:18:19] testing.INFO: Languages encodé directement: ["Espagnol","Allemand","Fran\u00e7ais"]  
[2025-07-08 14:18:19] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: array)  
[2025-07-08 14:18:19] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web"]  
[2025-07-08 14:18:19] testing.INFO: setSkillsAttribute appelé avec: ["CI\/CD","JavaScript","Java","Kubernetes"] (type: array)  
[2025-07-08 14:18:19] testing.INFO: Skills encodé directement: ["CI\/CD","JavaScript","Java","Kubernetes"]  
[2025-07-08 14:18:19] testing.INFO: setLanguagesAttribute appelé avec: ["Allemand","Anglais"] (type: array)  
[2025-07-08 14:18:19] testing.INFO: Languages encodé directement: ["Allemand","Anglais"]  
[2025-07-08 14:18:19] testing.INFO: setServicesOfferedAttribute appelé avec: ["Maintenance"] (type: array)  
[2025-07-08 14:18:19] testing.INFO: Services_offered encodé directement: ["Maintenance"]  
[2025-07-08 14:18:20] testing.INFO: setSkillsAttribute appelé avec: ["React","Kubernetes","Git","Python","AWS","Spring","Node.js","MongoDB"] (type: array)  
[2025-07-08 14:18:20] testing.INFO: Skills encodé directement: ["React","Kubernetes","Git","Python","AWS","Spring","Node.js","MongoDB"]  
[2025-07-08 14:18:20] testing.INFO: setLanguagesAttribute appelé avec: ["Allemand"] (type: array)  
[2025-07-08 14:18:20] testing.INFO: Languages encodé directement: ["Allemand"]  
[2025-07-08 14:18:20] testing.INFO: setServicesOfferedAttribute appelé avec: ["Maintenance","D\u00e9veloppement Web"] (type: array)  
[2025-07-08 14:18:20] testing.INFO: Services_offered encodé directement: ["Maintenance","D\u00e9veloppement Web"]  
[2025-07-08 14:18:20] testing.INFO: setSkillsAttribute appelé avec: ["MongoDB",".NET","AWS","Django","Git","Spring"] (type: array)  
[2025-07-08 14:18:20] testing.INFO: Skills encodé directement: ["MongoDB",".NET","AWS","Django","Git","Spring"]  
[2025-07-08 14:18:20] testing.INFO: setLanguagesAttribute appelé avec: ["Espagnol","Anglais","Fran\u00e7ais"] (type: array)  
[2025-07-08 14:18:20] testing.INFO: Languages encodé directement: ["Espagnol","Anglais","Fran\u00e7ais"]  
[2025-07-08 14:18:20] testing.INFO: setServicesOfferedAttribute appelé avec: ["Formation","D\u00e9veloppement Mobile","Maintenance"] (type: array)  
[2025-07-08 14:18:20] testing.INFO: Services_offered encodé directement: ["Formation","D\u00e9veloppement Mobile","Maintenance"]  
[2025-07-08 14:18:20] testing.INFO: setSkillsAttribute appelé avec: ["Django","Scrum","Laravel","PHP"] (type: array)  
[2025-07-08 14:18:20] testing.INFO: Skills encodé directement: ["Django","Scrum","Laravel","PHP"]  
[2025-07-08 14:18:20] testing.INFO: setLanguagesAttribute appelé avec: ["Allemand","Espagnol","Anglais"] (type: array)  
[2025-07-08 14:18:20] testing.INFO: Languages encodé directement: ["Allemand","Espagnol","Anglais"]  
[2025-07-08 14:18:20] testing.INFO: setServicesOfferedAttribute appelé avec: ["D\u00e9veloppement Web"] (type: array)  
[2025-07-08 14:18:20] testing.INFO: Services_offered encodé directement: ["D\u00e9veloppement Web"]  
[2025-07-08 18:55:07] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel","React","Vue.js","MySQL"] (type: array)  
[2025-07-08 18:55:07] local.INFO: Skills encodé directement: ["PHP","Laravel","React","Vue.js","MySQL"]  
[2025-07-08 18:55:07] local.INFO: setLanguagesAttribute appelé avec: ["French","English"] (type: array)  
[2025-07-08 18:55:07] local.INFO: Languages encodé directement: ["French","English"]  
[2025-07-08 18:55:07] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-08 18:55:07] local.INFO: getLanguagesAttribute appelé avec: ["French","English"] (type: string)  
[2025-07-08 18:55:07] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 18:55:07] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 18:56:09] local.INFO: setSkillsAttribute appelé avec: ["PHP","Laravel"] (type: array)  
[2025-07-08 18:56:09] local.INFO: Skills encodé directement: ["PHP","Laravel"]  
[2025-07-08 18:56:09] local.INFO: getLanguagesAttribute appelé avec: null (type: NULL)  
[2025-07-08 18:56:09] local.INFO: getLanguagesAttribute appelé avec: null (type: NULL)  
[2025-07-08 18:56:09] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
[2025-07-08 18:56:09] local.INFO: getServicesOfferedAttribute appelé avec: null (type: NULL)  
