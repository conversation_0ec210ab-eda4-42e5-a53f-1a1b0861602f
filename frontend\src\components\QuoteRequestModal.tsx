import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Header, DialogTitle } from './ui/dialog';
import { Button } from './ui/buttons';
import { Plus } from 'lucide-react';
import { API_BASE_URL } from '../config';
import { useParams, useNavigate } from 'react-router-dom';

interface QuoteRequestModalProps {
  token: string|undefined|null;
  user: any;
  pro: any;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const QuoteRequestModal = ({ token,user,pro, open, onOpenChange }: QuoteRequestModalProps) => {
  const [projectName, setProjectName] = useState('');
  const navigate = useNavigate();
  const [offers, setOffers] = useState([]);
  const [selectedOfferId, setSelectedOfferId] = useState(null);
  const [showModal, setShowModal] = useState(false);
    const [loadingInvite, setLoadingInvitation] = useState<boolean>(false);
  
    console.log("Utisateur: ",user);

  const getImageUrl = (imagePath: string | undefined, defaultImage: string = "https://randomuser.me/api/portraits/men/32.jpg") => {
      if (!imagePath) return defaultImage;
      if (imagePath.startsWith('http')) return imagePath;
      if (imagePath.startsWith('/')) return `${API_BASE_URL}${imagePath}`;
      return `${API_BASE_URL}/${imagePath}`;
    };

  
    // 2. Envoyer l'invitation
      const sendInvitation = async () => {
        if (!selectedOfferId) return alert("Sélectionne une offre.");
        if (!token) return alert("Vous devez vous connecter pour invité un pro.");
        setLoadingInvitation(true);
        try {
          const res = await fetch(`${API_BASE_URL}/api/open-offers/${selectedOfferId}/invite`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ professional_id: pro?.user_id }),
          });
    
          console.log("professional_id===========", pro?.user_id)
    
          const result = await res.json();
          if (res.ok) {
            alert("Invitation envoyée avec succès !");
            setShowModal(false);
          } else {
            alert("Erreur : " + result.message);
          }
        } catch (err) {
          console.error(err);
          alert("Une erreur est survenue.");
        } finally {
          setLoadingInvitation(false);
        }
      };

  // Fonction pour créer une offre ouverte avec invitation automatique
  const handleCreateOpenOffer = () => {
    // const token = localStorage.getItem('token');
    
    if(token){
      if(user && !user.is_professional) {
        navigate(`/dashboard/projects?invite=${pro?.user_id}&create=true`);
      }else{
         alert("Accès restreint : cette fonctionnalité est uniquement disponible pour les clients. Veuillez utiliser un compte client pour continuer.");
      }
    }else{
      navigate('/login');
    }

  };

  const handleInvitePro = () => {
    // const token = localStorage.getItem('token');
    if(token){
      if(user && !user.is_professional) {
        setShowModal(true);
      }else{
         alert("Accès restreint : cette fonctionnalité est uniquement disponible pour les clients. Veuillez utiliser un compte client pour continuer.");
      }
      
    }else{
      navigate('/login');
    }

  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl p-0 overflow-hidden">
        <div className="relative">
          {/* Hero Image */}
          <div className="w-full h-64 bg-gradient-to-br from-orange-200 to-orange-300 relative overflow-hidden">
            <img 
              src={getImageUrl(pro?.cover_photo, "/img/popup.png")}
              alt={`${pro?.first_name} ${pro?.last_name}`}
              className="w-full h-full object-cover"
              onError={(e) => {
                e.currentTarget.onerror = null; // empêche les boucles infinies
                e.currentTarget.src = '/img/popup.png'; // chemin de l'image par défaut
              }}
            />
          </div>
          
          {/* Content */}
          <div className="p-8 text-center bg-white">
            <DialogHeader className="mb-6">
              <DialogTitle className="text-2xl font-bold text-gray-900 mb-2 text-center">
                Request a quote from
              </DialogTitle>
              <h3 className="text-2xl font-bold text-gray-900 text-center">
                {`${pro?.first_name} ${pro?.last_name}`}
              </h3>
            </DialogHeader>
            
            <p className="text-gray-600 mb-8 max-w-md mx-auto leading-relaxed">
              One main advantage of using the application is: it helps you save 
              time by centralizing all your project information in one place.
            </p>
            
            <div className="space-y-4">
              {/* Create Quote Button */}
              <Button 
                className="w-full h-12 bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg flex items-center justify-center gap-3 text-base font-medium"
                variant="outline"
                onClick={handleCreateOpenOffer}
              >
                <Plus className="w-5 h-5" />
                Create a new quote request
              </Button>
              
              {/* Invite to Offer Button */}
              <Button 
                className="w-full h-12 bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg flex items-center justify-center gap-3 text-base font-medium"
                variant="outline"
                onClick={handleInvitePro}
              >
                <Plus className="w-5 h-5" />
                Invite to an open offer
              </Button>
              {/* <Button 
                className="w-full h-12 bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-lg flex items-center justify-start gap-3 px-4 text-base font-medium"
                variant="outline"
              >
                <Plus className="w-5 h-5" />
                <div className="flex items-center gap-2 text-left">
                  <span>Invite to an open offer</span>
                  <span className="text-gray-400">Project name</span>
                  <span className="text-gray-400">Offer selection drawer</span>
                </div>
              </Button> */}
            </div>
          </div>
        </div>

        {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-40 flex justify-center items-center z-50">
          <div className="bg-white p-6 rounded w-full max-w-md relative">
            <button onClick={() => setShowModal(false)} className="absolute top-2 right-2 text-gray-500">X</button>
            <h2 className="text-xl mb-4 font-bold">Sélectionner une offre</h2>

            <div className="space-y-2 max-h-60 overflow-y-auto">
              {offers.map((offer : any) => (
                <label key={offer.id} className="flex items-center space-x-2">
                  <input
                    type="radio"
                    value={offer.id}
                    checked={selectedOfferId === offer.id}
                    onChange={() => setSelectedOfferId(offer.id)}
                  />
                  <span>{offer.title}</span>
                </label>
              ))}
            </div>

            <button
              onClick={sendInvitation}
              className="mt-4 bg-green-600 text-white px-4 py-2 rounded"
              disabled={loadingInvite}
            >
              {loadingInvite ? 'Envoi...' : "Envoyer l'invitation"}
            </button>
          </div>
        </div>
      )}
      </DialogContent>
    </Dialog>
  );
};

export default QuoteRequestModal;