import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams, Link } from 'react-router-dom';
import { Mail, Lock, Github, Facebook, User } from 'lucide-react';
import { ApiError, testApiConnection } from '../../services/api';
import authService from '../../services/authService';
import { API_BASE_URL } from '../../config';
import { useProfileWizard } from '../../context/ProfileWizardContext';
import { useToast } from '../../context/ToastContext';
import Button from '../ui/Button';
import FormInput from '../ui/FormInput';
import Checkbox from '../ui/Checkbox';
import FormDivider from '../ui/FormDivider';
import SocialButton from '../ui/SocialButton';
import AuthFormContainer from './AuthFormContainer';
import './AuthButton.css';

interface LoginFormProps {
  onToggleForm?: () => void;
}

const LoginForm: React.FC<LoginFormProps> = ({ onToggleForm }): React.ReactElement => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { openProfileWizard } = useProfileWizard();
  const { showToast } = useToast();

  // Check if email has been verified
  useEffect(() => {
    if (searchParams.get('verified') === 'true') {
      setSuccessMessage('Votre adresse e-mail a été vérifiée avec succès !');
    }
  }, [searchParams]);

  // Form validation
  const [formErrors, setFormErrors] = useState({
    email: '',
    password: '',
  });

  const validateForm = () => {
    let isValid = true;
    const errors = {
      email: '',
      password: '',
    };

    // Email validation
    if (!email) {
      errors.email = 'L\'adresse email est obligatoire';
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      errors.email = 'L\'adresse email est invalide';
      isValid = false;
    }

    // Password validation
    if (!password) {
      errors.password = 'Le mot de passe est obligatoire';
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    console.log('Login form submitted');

    if (!validateForm()) {
      console.log('Form validation failed');
      return;
    }

    console.log('Form validation passed, attempting login with:', { email, password: '***' });
    setIsLoading(true);
    setError('');

    // Tester la connexion au serveur backend
    const isServerConnected = await testApiConnection();
    if (!isServerConnected) {
      const errorMessage = 'Impossible de se connecter au serveur backend. Vérifiez que le serveur est en cours d\'exécution à l\'adresse ' + API_BASE_URL;
      setError(errorMessage);
      showToast('error', errorMessage);
      setIsLoading(false);
      return;
    }

    try {
      console.log('Tentative de connexion...');

      // Utiliser notre service d'authentification pour la connexion
      const result = await authService.login({ email, password });
      console.log('Résultat de la connexion:', result);

      // Si nous arrivons ici, la connexion a réussi
      if (result && typeof result === 'object') {
        const typedResult = result as { user: any; token: string; message?: string };
        console.log('Données utilisateur reçues:', typedResult);

        if (typedResult.user && typedResult.token) {
          // Le service d'authentification a déjà stocké le token et les données utilisateur
          // et a initialisé la variable first_login

          if (rememberMe) {
            localStorage.setItem('rememberedEmail', email);
          }

          showToast('success', 'Connexion réussie !');
          navigate('/dashboard');
          openProfileWizard();
        } else if (typedResult.message) {
          setError(typedResult.message);
          showToast('error', typedResult.message);
        } else {
          const errorMessage = 'Une erreur est survenue lors de la connexion';
          setError(errorMessage);
          showToast('error', errorMessage);
        }
      } else {
        const errorMessage = 'Format de réponse invalide';
        setError(errorMessage);
        showToast('error', errorMessage);
      }
    } catch (err) {
      console.error('Erreur lors de la connexion:', err);

      let errorMessage = 'Une erreur inconnue est survenue. Vérifiez que le serveur backend est en cours d\'exécution.';

      if ((err as ApiError).message) {
        const apiError = err as ApiError;
        errorMessage = apiError.message;
        
        // Gestion spécifique des erreurs d'authentification
        if (apiError.status === 401) {
          errorMessage = 'Email ou mot de passe incorrect';
        } else if (apiError.status === 403) {
          errorMessage = 'Votre e-mail n\'est pas vérifié. Veuillez vérifier votre boîte de réception.';
        }
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }

      console.log('Message d\'erreur:', errorMessage);
      setError(errorMessage);
      showToast('error', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthFormContainer
      title="Connexion"
      subtitle="Connectez-vous à votre compte pour accéder à votre espace"
      icon={<User className="w-full h-full" />}
      error={error}
      successMessage={successMessage}
    >

      {/* Login form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <FormInput
          label="Adresse email"
          type="email"
          id="email"
          placeholder="<EMAIL>"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          icon={<Mail className="h-5 w-5 text-neutral-400" />}
          error={formErrors.email}
          required
        />

        <FormInput
          label="Mot de passe"
          type="password"
          id="password"
          placeholder="••••••••"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          icon={<Lock className="h-5 w-5 text-neutral-400" />}
          error={formErrors.password}
          showPasswordToggle
          required
        />

        <div className="flex items-center justify-between">
          <Checkbox
            id="remember-me"
            label="Se souvenir de moi"
            checked={rememberMe}
            onChange={() => setRememberMe(!rememberMe)}
          />

          <Link
            to="/forgot-password"
            className="text-sm font-medium text-primary-600 hover:text-primary-700"
          >
            Mot de passe oublié ?
          </Link>
        </div>

        <Button
          type="submit"
          variant="primary"
          fullWidth
          disabled={isLoading}
          aria-busy={isLoading}
          className="auth-button"
          style={{ color: 'black' }}
        >
          {isLoading ? 'Chargement...' : 'Se connecter'}
        </Button>
      </form>

      <FormDivider text="Ou continuer avec" />

      <div className="grid grid-cols-2 gap-3 mt-6">
        <SocialButton
          icon={<Github className="h-5 w-5" />}
          provider="Github"
        />
        <SocialButton
          icon={<Facebook className="h-5 w-5 text-blue-600" />}
          provider="Facebook"
        />
      </div>

      <p className="mt-8 text-center text-sm text-neutral-600">
        Vous n'avez pas de compte ?{' '}
        {onToggleForm ? (
          <button
            type="button"
            onClick={onToggleForm}
            className="font-medium text-primary-600 hover:text-primary-700"
          >
            Créer un compte
          </button>
        ) : (
          <Link
            to="/register"
            className="font-medium text-primary-600 hover:text-primary-700"
          >
            Créer un compte
          </Link>
        )}
      </p>
    </AuthFormContainer>
  );
};

export default LoginForm;
