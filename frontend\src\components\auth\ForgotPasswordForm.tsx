import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Mail, AlertCircle, CheckCircle, ArrowLeft } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import { useToast } from '../../context/ToastContext';
import Button from '../ui/Button';
import FormInput from '../ui/FormInput';
import { ApiError } from '../../services/api';

const ForgotPasswordForm: React.FC = () => {
  const navigate = useNavigate();
  const { showToast } = useToast();
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Form validation
  const [formErrors, setFormErrors] = useState({
    email: '',
  });

  const validateForm = () => {
    let isValid = true;
    const errors = {
      email: '',
    };

    // Email validation
    if (!email) {
      errors.email = 'L\'adresse email est obligatoire';
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      errors.email = 'L\'adresse email est invalide';
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!validateForm()) {
      // Display toast for validation errors
      if (formErrors.email) {
        showToast('error', formErrors.email);
      }
      return;
    }

    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch(`${API_BASE_URL}/api/password/forgot`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const result = await response.json();

      if (response.ok) {
        const successMessage = result.message || 'Un email de réinitialisation a été envoyé à votre adresse email.';
        setSuccess(successMessage);
        showToast('success', successMessage);
        setEmail('');
      } else {
        let errorMessage: string;
        
        const apiError = result as ApiError;

        if (response.status === 422) {
          if (apiError.errors && apiError.errors.email && Array.isArray(apiError.errors.email) && apiError.errors.email.length > 0) {
            errorMessage = apiError.errors.email[0]; // Message spécifique de l'erreur d'email
          } else if (apiError.message) {
            errorMessage = apiError.message; // Fallback au message général si présent
          } else {
            errorMessage = 'Veuillez vérifier l\'adresse email saisie.'; // Message générique pour les erreurs 422
          }
        } else if (response.status === 404) {
          errorMessage = apiError.message || 'Aucun utilisateur trouvé avec cette adresse e-mail.';
        } else {
          errorMessage = apiError.message || 'Une erreur est survenue lors de l\'envoi de l\'email de réinitialisation.';
        }

        setError(errorMessage);
        showToast('error', errorMessage);
      }
    } catch (err) {
      const networkErrorMessage = 'Erreur de connexion à l\'API. Veuillez vérifier votre connexion internet ou que le serveur backend est en cours d\'exécution.';
      setError(networkErrorMessage);
      showToast('error', networkErrorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-2xl font-bold text-neutral-900">Mot de passe oublié</h1>
        <p className="text-neutral-600 mt-2">
          Entrez votre adresse email pour recevoir un lien de réinitialisation
        </p>
      </div>

      {/* Success message */}
      {success && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
          <p className="text-green-700">{success}</p>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* Forgot password form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <FormInput
          label="Adresse email"
          type="email"
          id="email"
          placeholder="<EMAIL>"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          icon={<Mail className="h-5 w-5 text-neutral-400" />}
          error={formErrors.email}
          required
        />

        <Button
          type="submit"
          variant="primary"
          fullWidth
          isLoading={isLoading}
        >
          Envoyer le lien de réinitialisation
        </Button>
      </form>

      <div className="mt-8 text-center">
        <Link
          to="/login"
          className="inline-flex items-center text-sm font-medium text-primary-600 hover:text-primary-700"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Retour à la connexion
        </Link>
      </div>
    </div>
  );
};

export default ForgotPasswordForm;
