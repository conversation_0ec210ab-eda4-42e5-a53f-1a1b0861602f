import React, { useRef, useState, useEffect } from 'react';

type Props = {
  categories: string[];
  onCategorySelect: (category: string) => void;
  selectedCategory:string;
};

const CategoryList: React.FC<Props> = ({ categories,onCategorySelect,selectedCategory}) => {
  const [showFilters, setShowFilters] = React.useState(false);
  const [showChevron, setShowChevron] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Show chevron if scrollable
  useEffect(() => {
    const el = scrollRef.current;
    if (el) {
      setShowChevron(el.scrollWidth > el.clientWidth);
    }
  }, [categories]);

  // Scroll right on chevron click
  const handleChevronClick = () => {
    const el = scrollRef.current;
    if (el) {
      el.scrollBy({ left: 120, behavior: 'smooth' });
    }
  };

  return (
    <div className="w-full max-w-[1357px] mx-auto my-8 px-4 font-['Arial'] text-[15px] relative">
      {/* Desktop layout */}
      <div className="hidden md:flex items-center justify-between h-[89px]">
        {/* Left Dropdown */}
        <button className="border border-gray-200 rounded-lg px-4 py-2 bg-white font-semibold text-[13px] text-zinc-900 cursor-pointer min-w-[90px] font-['Arial'] flex items-center">
          <span>Popular</span>
          <svg width="14" height="14" viewBox="0 0 20 20" fill="none" className="ml-1.5 inline-block">
            <path d="M6 8L10 12L14 8" stroke="#a1a1aa" strokeWidth="2.2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
        {/* Centered Categories */}
        <div className="flex gap-6 justify-center flex-1">
          {categories.map((cat) => (
            <button
              key={cat}
              onClick={() => onCategorySelect(cat)}
              className={`rounded-3xl px-2.5 py-2 font-semibold text-[13px] text-zinc-900 cursor-pointer transition-colors duration-200 font-['Arial'] ${
                selectedCategory === cat || (!selectedCategory && cat === 'All')
                  ? 'bg-[#f7f6f2] shadow-[0_2px_8px_rgba(0,0,0,0.04)]'
                  : 'bg-transparent'
              }`}
            >
              {cat}
            </button>
          ))}
          {/* {categories.map((cat, idx) => (
            <button
              key={cat}
              className={`rounded-3xl px-2.5 py-2 font-semibold text-[13px] text-zinc-900 cursor-pointer transition-colors duration-200 font-['Arial'] ${
                idx === 0 
                  ? 'bg-[#f7f6f2] shadow-[0_2px_8px_rgba(0,0,0,0.04)]' 
                  : 'bg-transparent'
              }`}
              onClick={() => onCategorySelect(cat)}
            >
              {cat}
            </button>
          ))} */}
        </div>
        {/* Right Filter Button */}
        <div className="relative">
          <button
            className="border border-gray-200 rounded-3xl px-6 py-2 bg-white font-medium text-[14px] text-zinc-900 cursor-pointer flex items-center min-w-[90px] font-['Arial']"
            onClick={() => setShowFilters((prev) => !prev)}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              className="mr-2"
            >
              <path
                d="M3 6.5C3 6.22386 3.22386 6 3.5 6H16.5C16.7761 6 17 6.22386 17 6.5C17 6.77614 16.7761 7 16.5 7H3.5C3.22386 7 3 6.77614 3 6.5ZM5 10.5C5 10.2239 5.22386 10 5.5 10H14.5C14.7761 10 15 10.2239 15 10.5C15 10.7761 14.7761 11 14.5 11H5.5C5.22386 11 5 10.7761 5 10.5ZM8 14.5C8 14.2239 8.22386 14 8.5 14H11.5C11.7761 14 12 14.2239 12 14.5C12 14.7761 11.7761 15 11.5 15H8.5C8.22386 15 8 14.7761 8 14.5Z"
                fill="#18181b"
              />
            </svg>
            <span className="hidden sm:inline">Filters</span>
            <span className="sm:hidden">Filter</span>
          </button>
          {showFilters && (
            <div className="absolute top-12 right-0 bg-white border border-gray-200 rounded-lg p-6 shadow-[0_4px_24px_rgba(0,0,0,0.08)] z-50 min-w-[260px] w-[280px] sm:w-auto">
              <div className="mb-4">
                <label className="font-medium">
                  Tags<br />
                  <input 
                    type="text" 
                    placeholder="Search tags..." 
                    className="w-full mt-1 mb-3 px-1.5 py-1.5 rounded border border-gray-200 text-sm" 
                  />
                </label>
              </div>
              <div className="mb-4">
                <label className="font-medium">
                  Color<br />
                  <input 
                    type="text" 
                    placeholder="Enter hex or select" 
                    className="w-full mt-1 mb-3 px-1.5 py-1.5 rounded border border-gray-200 text-sm" 
                  />
                </label>
              </div>
              <div className="mb-4">
                <label className="font-medium">
                  Timeframe<br />
                  <select className="w-full mt-1 px-1.5 py-1.5 rounded border border-gray-200 text-sm">
                    <option>Select a Timeframe</option>
                    <option>Today</option>
                    <option>This week</option>
                    <option>This month</option>
                  </select>
                </label>
              </div>
              <button
                className="mt-2 bg-[#f7f6f2] border-none rounded-md px-4 py-2 font-semibold cursor-pointer w-full"
                onClick={() => setShowFilters(false)}
              >
                Fermer
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Mobile layout */}
      <div className="md:hidden w-full">
        <div className="flex items-center justify-between mb-2">
          {/* Popular left */}
          <button className="border border-gray-200 rounded-lg px-4 py-2 bg-white font-semibold text-[13px] text-zinc-900 cursor-pointer min-w-[90px] font-['Arial'] flex items-center">
            <span>Popular</span>
            <svg width="14" height="14" viewBox="0 0 20 20" fill="none" className="ml-1.5 inline-block">
              <path d="M6 8L10 12L14 8" stroke="#a1a1aa" strokeWidth="2.2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
          {/* Filters right */}
          <div className="relative">
            <button
              className="border border-gray-200 rounded-3xl px-4 py-2 bg-white font-medium text-[14px] text-zinc-900 cursor-pointer flex items-center min-w-[90px] font-['Arial']"
              onClick={() => setShowFilters((prev) => !prev)}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                className="mr-2"
              >
                <path
                  d="M3 6.5C3 6.22386 3.22386 6 3.5 6H16.5C16.7761 6 17 6.22386 17 6.5C17 6.77614 16.7761 7 16.5 7H3.5C3.22386 7 3 6.77614 3 6.5ZM5 10.5C5 10.2239 5.22386 10 5.5 10H14.5C14.7761 10 15 10.2239 15 10.5C15 10.7761 14.7761 11 14.5 11H5.5C5.22386 11 5 10.7761 5 10.5ZM8 14.5C8 14.2239 8.22386 14 8.5 14H11.5C11.7761 14 12 14.2239 12 14.5C12 14.7761 11.7761 15 11.5 15H8.5C8.22386 15 8 14.7761 8 14.5Z"
                  fill="#18181b"
                />
              </svg>
              Filter
            </button>
            {showFilters && (
              <div className="absolute top-12 right-0 bg-white border border-gray-200 rounded-lg p-6 shadow-[0_4px_24px_rgba(0,0,0,0.08)] z-50 min-w-[260px] w-[90vw] max-w-xs">
                <div className="mb-4">
                  <label className="font-medium">
                    Tags<br />
                    <input 
                      type="text" 
                      placeholder="Search tags..." 
                      className="w-full mt-1 mb-3 px-1.5 py-1.5 rounded border border-gray-200 text-sm" 
                    />
                  </label>
                </div>
                <div className="mb-4">
                  <label className="font-medium">
                    Color<br />
                    <input 
                      type="text" 
                      placeholder="Enter hex or select" 
                      className="w-full mt-1 mb-3 px-1.5 py-1.5 rounded border border-gray-200 text-sm" 
                    />
                  </label>
                </div>
                <div className="mb-4">
                  <label className="font-medium">
                    Timeframe<br />
                    <select className="w-full mt-1 px-1.5 py-1.5 rounded border border-gray-200 text-sm">
                      <option>Select a Timeframe</option>
                      <option>Today</option>
                      <option>This week</option>
                      <option>This month</option>
                    </select>
                  </label>
                </div>
                <button
                  className="mt-2 bg-[#f7f6f2] border-none rounded-md px-4 py-2 font-semibold cursor-pointer w-full"
                  onClick={() => setShowFilters(false)}
                >
                  Fermer
                </button>
              </div>
            )}
          </div>
        </div>
        {/* Category list scrollable with blue chevron only on the right for mobile */}
        <div className="relative">
          <div
            ref={scrollRef}
            className="flex gap-4 overflow-x-auto scrollbar-hide pr-7 h-9"
            style={{ WebkitOverflowScrolling: 'touch', scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {categories.map((cat, idx) => (
              <button
                key={cat}
                className={`rounded-3xl px-2.5 py-2 font-semibold text-[13px] text-zinc-900 cursor-pointer transition-colors duration-200 font-['Arial'] whitespace-nowrap flex-shrink-0 h-9 flex items-center ${
                  idx === 0 
                    ? 'bg-[#f7f6f2] shadow-[0_2px_8px_rgba(0,0,0,0.04)]' 
                    : 'bg-transparent'
                }`}
              >
                {cat}
              </button>
            ))}
          </div>
          {/* Blue Chevron only on the right, outside the white bg, only on mobile */}
          {showChevron && (
            <button
              className="absolute right-0 top-0 h-9 flex items-center justify-center z-10 md:hidden"
              onClick={handleChevronClick}
              aria-label="Scroll right"
            >
              <svg width="28" height="28" fill="none" viewBox="0 0 24 24">
                <path d="M10 8l4 4-4 4" stroke="#18181b" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CategoryList; 