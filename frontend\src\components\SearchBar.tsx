import React, { useState, useRef, useEffect } from 'react';
import { Search } from 'lucide-react';

const options = ['3D Artiste', 'Services'];

interface SearchBarProps {
  onSearch: (query: string, type: string) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({ onSearch }) => {
  const [query, setQuery] = useState('');
  const [type, setType] = useState('Search By');
  const [open, setOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Déclenche la recherche automatique à chaque frappe
  useEffect(() => {
    const delayDebounce = setTimeout(() => {
      onSearch(query.trim().toLowerCase(), type);
    }, 300); // debounce : 300ms

    return () => clearTimeout(delayDebounce);
  }, [query, type]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

   const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(query.trim().toLowerCase(), type);
  };

  return (
    <form
      style={{
        display: 'flex',
        alignItems: 'center',
        background: '#f7f7fa',
        borderRadius: '2rem',
        padding: '0.5rem 1rem',
        minWidth: 680,
        boxShadow: '0 1px 4px rgba(0,0,0,0.03)'
      }}
      onSubmit={handleSubmit}
    >
      <input
        type="text"
        placeholder="What are you looking for?"
        value={query}
        onChange={e => setQuery(e.target.value)}
        style={{
          border: 'none',
          outline: 'none',
          background: 'transparent',
          fontSize: '1rem',
          flex: 1,
          padding: '0.3rem 1rem',
        }}
      />
      <div style={{ position: 'relative', marginRight: '0.5rem' }} ref={menuRef}>
        <div
          style={{
            fontWeight: 600,
            fontSize: '0.9rem',
            color: '#18182F',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            userSelect: 'none'
          }}
          onClick={() => setOpen(o => !o)}
        >
          {type}
          <svg
            style={{
              marginLeft: 6,
              width: 18,
              height: 18,
              transition: 'transform 0.2s',
              transform: open ? 'rotate(180deg)' : 'rotate(0deg)',
              display: 'inline',
              verticalAlign: 'middle',
            }}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M6 8L10 12L14 8" stroke="#48485A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </div>
        {open && (
          <div
            style={{
              position: 'absolute',
              top: '120%',
              left: 0,
              background: '#fff',
              borderRadius: 16,
              boxShadow: '0 4px 24px rgba(0,0,0,0.08)',
              padding: '1rem 0.5rem',
              minWidth: 180,
              zIndex: 10,
            }}
          >
            {options.map(opt => (
              <div
                key={opt}
                onClick={() => { setType(opt); setOpen(false); }}
                style={{
                  padding: '0.75rem 1.5rem',
                  fontWeight: type === opt ? 700 : 400,
                  color: '#18182F',
                  background: type === opt ? '#f7f7fa' : 'transparent',
                  cursor: 'pointer',
                  borderRadius: 12,
                  fontSize: '1.1rem',
                  transition: 'background 0.15s',
                }}
              >
                {opt}
              </div>
            ))}
          </div>
        )}
      </div>
      <button
        type="submit"
        style={{
          background: '#ea4c89',
          border: 'none',
          borderRadius: '50%',
          width: 56,
          height: 56,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
        }}
      >
        <img
          src="/img/icone/icons8-search-500.png"
          alt="search"
          style={{ width: 38, height: 38, objectFit: 'contain' }}
        />
      </button>
    </form>
  );
};

export default SearchBar; 