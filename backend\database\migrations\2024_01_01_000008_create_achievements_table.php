<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('achievements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('professional_profile_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->string('organization')->nullable();
            $table->date('date_obtained')->nullable();
            $table->text('description')->nullable();
            $table->string('file_path')->nullable(); // Maintenu pour la rétrocompatibilité
            $table->json('files')->nullable(); // Nouveau champ pour plusieurs fichiers
            $table->string('achievement_url')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('achievements');
    }
};
