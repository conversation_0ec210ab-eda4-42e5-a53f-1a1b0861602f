import React from 'react';
import SearchBar from './SearchBar';

interface HeroProps {
  onSearch: (query: string, type: string) => void;
}

const Hero: React.FC<HeroProps> = ({ onSearch }) => {
  return (
    <div
      className="bg-white pt-24 md:pt-40 text-center px-4"
      style={{ background: '#fff', textAlign: 'center' }}
    >
      <h1
        className="text-3xl md:text-5xl font-normal mb-6"
        style={{ fontSize: '3.1rem', fontWeight: 400, fontFamily: 'Merriweather, serif', color: '#18182F', letterSpacing: '0.03em', lineHeight: '1.3' }}
      >
        Discover the world's<br />top designers
      </h1>
      <p
        className="text-base md:text-lg mb-10 font-normal"
        style={{ fontSize: '1rem', color: '#18182F', fontWeight: 400 }}
      >
        Explore work from the most talented and accomplished designers<br />ready to take on your next project
      </p>
      <div className="flex justify-center mt-10">
        <SearchBar onSearch={onSearch}/>
      </div>
    </div>
  );
};

export default Hero; 