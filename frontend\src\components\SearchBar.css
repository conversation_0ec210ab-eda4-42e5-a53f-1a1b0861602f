.search-bar-form {
  display: flex;
  align-items: center;
  background: #f7f7fa;
  border-radius: 2rem;
  padding: 0.5rem 1rem;
  min-width: 280px;
  max-width: 680px;
  width: 100%;
  box-shadow: 0 1px 4px rgba(0,0,0,0.03);
  flex-direction: row;
  gap: 0;
}

.search-bar-input {
  border: none;
  outline: none;
  background: transparent;
  font-size: 1rem;
  flex: 1;
  padding: 0.3rem 1rem;
  width: 100%;
}

.search-bar-dropdown {
  position: relative;
  margin-right: 0.5rem;
  width: auto;
}

.search-bar-dropdown-trigger {
  font-weight: 600;
  font-size: 0.9rem;
  color: #18182F;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  user-select: none;
  padding: 0;
}

.search-bar-dropdown-arrow {
  margin-left: 6px;
  width: 18px;
  height: 18px;
  transition: transform 0.2s;
  display: inline;
  vertical-align: middle;
}

.search-bar-dropdown-arrow.open {
  transform: rotate(180deg);
}

.search-bar-dropdown-menu {
  position: absolute;
  top: 120%;
  left: 0;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
  padding: 1rem 0.5rem;
  min-width: 180px;
  width: auto;
  z-index: 10;
}

.search-bar-dropdown-option {
  padding: 0.75rem 1.5rem;
  font-weight: 400;
  color: #18182F;
  background: transparent;
  cursor: pointer;
  border-radius: 12px;
  font-size: 1.1rem;
  transition: background 0.15s;
  text-align: left;
}

.search-bar-dropdown-option.selected {
  font-weight: 700;
  background: #f7f7fa;
}

.search-bar-button {
  background: #ea4c89;
  border: none;
  border-radius: 50%;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  align-self: auto;
}

.search-bar-button img {
  width: 38px;
  height: 38px;
  object-fit: contain;
}

/* Responsive styles */
@media (max-width: 768px) {
  .search-bar-form {
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.75rem;
    border-radius: 1rem;
  }

  .search-bar-input {
    font-size: 0.9rem;
    padding: 0.5rem;
    text-align: center;
  }

  .search-bar-dropdown {
    margin-right: 0;
    width: 100%;
  }

  .search-bar-dropdown-trigger {
    justify-content: center;
    padding: 0.5rem;
    font-size: 0.85rem;
  }

  .search-bar-dropdown-menu {
    left: 50%;
    transform: translateX(-50%);
    min-width: 200px;
    width: 100%;
  }

  .search-bar-dropdown-option {
    text-align: center;
    font-size: 1rem;
    padding: 0.6rem 1rem;
  }

  .search-bar-button {
    width: 48px;
    height: 48px;
    align-self: center;
  }

  .search-bar-button img {
    width: 32px;
    height: 32px;
  }
}

@media (max-width: 480px) {
  .search-bar-form {
    min-width: 250px;
    padding: 0.5rem;
  }

  .search-bar-input {
    font-size: 0.85rem;
    padding: 0.4rem;
  }

  .search-bar-dropdown-trigger {
    font-size: 0.8rem;
    padding: 0.4rem;
  }

  .search-bar-dropdown-arrow {
    width: 16px;
    height: 16px;
  }

  .search-bar-dropdown-menu {
    min-width: 180px;
    padding: 0.75rem 0.5rem;
  }

  .search-bar-dropdown-option {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
  }

  .search-bar-button {
    width: 44px;
    height: 44px;
  }

  .search-bar-button img {
    width: 28px;
    height: 28px;
  }
} 