{"info": {"name": "Multiple Files - Achievements & Services", "description": "Collection pour tester l'upload de fichiers multiples pour les achievements et services", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "type": "string"}, {"key": "token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('token', response.token);", "    pm.test('Login successful', function () {", "        pm.expect(response.success).to.be.true;", "        pm.expect(response.token).to.not.be.empty;", "    });", "} else {", "    pm.test('<PERSON><PERSON> failed', function () {", "        pm.expect.fail('<PERSON><PERSON> request failed');", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password\"\n}"}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}}}]}, {"name": "Achievements", "item": [{"name": "Create Achievement - Multiple Files", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Certification AWS Solutions Architect", "type": "text"}, {"key": "organization", "value": "Amazon Web Services", "type": "text"}, {"key": "date_obtained", "value": "2024-01-15", "type": "text"}, {"key": "description", "value": "Certification professionnelle en architecture cloud", "type": "text"}, {"key": "achievement_url", "value": "https://aws.amazon.com/certification/", "type": "text"}, {"key": "files[]", "type": "file", "src": []}, {"key": "files[]", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/achievements", "host": ["{{base_url}}"], "path": ["achievements"]}}}, {"name": "Create Achievement - Single File (Legacy)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Formation React Advanced", "type": "text"}, {"key": "organization", "value": "Tech Academy", "type": "text"}, {"key": "date_obtained", "value": "2024-01-10", "type": "text"}, {"key": "description", "value": "Formation avancée en React.js", "type": "text"}, {"key": "file", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/achievements", "host": ["{{base_url}}"], "path": ["achievements"]}}}, {"name": "Get All Achievements", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/achievements", "host": ["{{base_url}}"], "path": ["achievements"]}}}, {"name": "Get Achievement by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/achievements/1", "host": ["{{base_url}}"], "path": ["achievements", "1"]}}}, {"name": "Update Achievement", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Certification AWS Solutions Architect - Updated", "type": "text"}, {"key": "description", "value": "Certification mise à jour avec nouvelles compétences", "type": "text"}, {"key": "files[]", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/achievements/1", "host": ["{{base_url}}"], "path": ["achievements", "1"]}}}, {"name": "Download Achievement File", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/achievements/1/download?file_index=0", "host": ["{{base_url}}"], "path": ["achievements", "1", "download"], "query": [{"key": "file_index", "value": "0"}]}}}, {"name": "Delete Achievement", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/achievements/1", "host": ["{{base_url}}"], "path": ["achievements", "1"]}}}]}, {"name": "Services", "item": [{"name": "Create Service - Multiple Files", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "Développement Application Mobile", "type": "text"}, {"key": "description", "value": "Création d'applications mobiles iOS et Android", "type": "text"}, {"key": "price", "value": "2500.00", "type": "text"}, {"key": "execution_time", "value": "4-6 semaines", "type": "text"}, {"key": "concepts", "value": "3 concepts inclus", "type": "text"}, {"key": "revisions", "value": "2 révisions gratuites", "type": "text"}, {"key": "categories[]", "value": "Développement Mobile", "type": "text"}, {"key": "categories[]", "value": "UI/UX Design", "type": "text"}, {"key": "is_private", "value": "false", "type": "text"}, {"key": "status", "value": "published", "type": "text"}, {"key": "files[]", "type": "file", "src": []}, {"key": "files[]", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/service-offers", "host": ["{{base_url}}"], "path": ["service-offers"]}}}, {"name": "Get All Services", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/service-offers", "host": ["{{base_url}}"], "path": ["service-offers"]}}}, {"name": "Get Service by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/service-offers/1", "host": ["{{base_url}}"], "path": ["service-offers", "1"]}}}, {"name": "Download Service File", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/service-offers/1/download?file_index=0", "host": ["{{base_url}}"], "path": ["service-offers", "1", "download"], "query": [{"key": "file_index", "value": "0"}]}}}]}]}