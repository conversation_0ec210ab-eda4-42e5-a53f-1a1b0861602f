[2025-07-08 14:10:52] local.DEBUG: Performance {"url":"http://localhost:8000/api/health-check","method":"GET","execution_time":"31.08 ms","memory_usage":"0.74 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:11:13] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/view","method":"POST","execution_time":"15.5 ms","memory_usage":"0.71 MB","status_code":500,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:11:29] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/view/stats","method":"GET","execution_time":"39.13 ms","memory_usage":"0.86 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:11:48] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/like","method":"POST","execution_time":"38.64 ms","memory_usage":"1.14 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:12:12] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/like/status","method":"GET","execution_time":"27.77 ms","memory_usage":"1.13 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:12:31] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/like/toggle","method":"POST","execution_time":"37.75 ms","memory_usage":"1.13 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:12:53] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/like","method":"POST","execution_time":"48.95 ms","memory_usage":"1.14 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:13:39] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/like","method":"DELETE","execution_time":"37.31 ms","memory_usage":"1.13 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:13:57] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/view","method":"POST","execution_time":"13.02 ms","memory_usage":"0.71 MB","status_code":500,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:14:45] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/view/status","method":"GET","execution_time":"14.42 ms","memory_usage":"0.71 MB","status_code":500,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:15:26] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/view","method":"POST","execution_time":"41.34 ms","memory_usage":"0.85 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:15:38] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/view/status","method":"GET","execution_time":"22.77 ms","memory_usage":"0.85 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:15:53] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/view/stats","method":"GET","execution_time":"40.1 ms","memory_usage":"0.86 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:16:46] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/view/stats","method":"GET","execution_time":"39.11 ms","memory_usage":"0.86 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:17:15] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/1/like","method":"POST","execution_time":"62.47 ms","memory_usage":"1.28 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:17:15] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/2/like","method":"DELETE","execution_time":"13.63 ms","memory_usage":"0.01 MB","status_code":200,"user_id":3,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:17:16] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/3/like/toggle","method":"POST","execution_time":"26.8 ms","memory_usage":"0.02 MB","status_code":200,"user_id":5,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:17:16] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/3/like/toggle","method":"POST","execution_time":"12.07 ms","memory_usage":"0.01 MB","status_code":200,"user_id":5,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:17:17] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/5/like/status","method":"GET","execution_time":"33.72 ms","memory_usage":"0.01 MB","status_code":200,"user_id":9,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:17:17] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/5/like/status","method":"GET","execution_time":"4.38 ms","memory_usage":"0.01 MB","status_code":200,"user_id":9,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:18:19] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/1/like","method":"POST","execution_time":"43.67 ms","memory_usage":"1.28 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:18:19] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/2/like","method":"DELETE","execution_time":"9.72 ms","memory_usage":"0.01 MB","status_code":200,"user_id":3,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:18:20] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/3/like/toggle","method":"POST","execution_time":"17.91 ms","memory_usage":"0.02 MB","status_code":200,"user_id":5,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:18:20] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/3/like/toggle","method":"POST","execution_time":"10.09 ms","memory_usage":"0.01 MB","status_code":200,"user_id":5,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:18:20] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/5/like/status","method":"GET","execution_time":"10.91 ms","memory_usage":"0.01 MB","status_code":200,"user_id":9,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:18:20] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/5/like/status","method":"GET","execution_time":"3.44 ms","memory_usage":"0.01 MB","status_code":200,"user_id":9,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 17:43:10] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/6/like/status","method":"GET","execution_time":"94.17 ms","memory_usage":"1.13 MB","status_code":200,"user_id":11,"ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 18:56:33] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"275.81 ms","memory_usage":"2.6 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:12:04] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"2120.83 ms","memory_usage":"4.77 MB","status_code":500,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:12:04] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"2120.83 ms","memory_usage":"4.77 MB","status_code":500,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:14:26] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"273.15 ms","memory_usage":"2.61 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:14:43] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/suggestions?limit=5&q=Lar","method":"GET","execution_time":"2422.27 ms","memory_usage":"3.48 MB","status_code":500,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:14:43] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/suggestions?limit=5&q=Lar","method":"GET","execution_time":"2422.27 ms","memory_usage":"3.48 MB","status_code":500,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:17:17] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"40.12 ms","memory_usage":"0.68 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:17:18] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/popular","method":"GET","execution_time":"88.1 ms","memory_usage":"1.61 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:17:19] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/metrics/realtime","method":"GET","execution_time":"24.46 ms","memory_usage":"0.69 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:17:19] local.DEBUG: Performance {"url":"http://localhost:8000/api/search","method":"GET","execution_time":"37.99 ms","memory_usage":"1.43 MB","status_code":302,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:17:20] local.DEBUG: Performance {"url":"http://localhost:8000/api/search?q=a","method":"GET","execution_time":"28.84 ms","memory_usage":"1.7 MB","status_code":302,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:17:20] local.DEBUG: Performance {"url":"http://localhost:8000/api/search?q=test&types%5B0%5D=invalid_type","method":"GET","execution_time":"29.59 ms","memory_usage":"1.7 MB","status_code":302,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:19:56] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"32.32 ms","memory_usage":"0.68 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:21:38] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"35.77 ms","memory_usage":"0.68 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:22:21] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/metrics/realtime","method":"GET","execution_time":"21.38 ms","memory_usage":"0.69 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:23:51] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"51.68 ms","memory_usage":"0.68 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:46:28] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"315.59 ms","memory_usage":"2.61 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:46:29] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/popular","method":"GET","execution_time":"74.7 ms","memory_usage":"1.61 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:46:30] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/metrics/realtime","method":"GET","execution_time":"39.23 ms","memory_usage":"0.69 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:46:36] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search?q=Meilisearch","method":"GET","execution_time":"2136.77 ms","memory_usage":"5.58 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:46:36] local.DEBUG: Performance {"url":"http://localhost:8000/api/search?q=Meilisearch","method":"GET","execution_time":"2136.77 ms","memory_usage":"5.58 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:46:38] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/professionals?q=Expert","method":"GET","execution_time":"1240.39 ms","memory_usage":"5.31 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:46:38] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/professionals?q=Expert","method":"GET","execution_time":"1240.39 ms","memory_usage":"5.31 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:46:39] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/services?q=Integration","method":"GET","execution_time":"1141.91 ms","memory_usage":"5.2 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:46:39] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/services?q=Integration","method":"GET","execution_time":"1141.91 ms","memory_usage":"5.2 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:46:41] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/achievements?q=Certified","method":"GET","execution_time":"1190.41 ms","memory_usage":"5.29 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:46:41] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/achievements?q=Certified","method":"GET","execution_time":"1190.41 ms","memory_usage":"5.29 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:46:44] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/suggestions?limit=5&q=Meili","method":"GET","execution_time":"2081.46 ms","memory_usage":"5.28 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:46:44] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/suggestions?limit=5&q=Meili","method":"GET","execution_time":"2081.46 ms","memory_usage":"5.28 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:47:05] local.DEBUG: Performance {"url":"http://localhost:8000/api/search?q=Meilisearch","method":"GET","execution_time":"87.18 ms","memory_usage":"1.99 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:47:50] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/professionals?filters%5Bcity%5D=Lyon&q=Expert","method":"GET","execution_time":"1339.1 ms","memory_usage":"3.51 MB","status_code":500,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:47:50] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/professionals?filters%5Bcity%5D=Lyon&q=Expert","method":"GET","execution_time":"1339.1 ms","memory_usage":"3.51 MB","status_code":500,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:48:19] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/suggestions?limit=5&q=Meili","method":"GET","execution_time":"6684.4 ms","memory_usage":"1.89 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:48:19] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/suggestions?limit=5&q=Meili","method":"GET","execution_time":"6684.4 ms","memory_usage":"1.89 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:52:21] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/professionals?filters%5Bcity%5D=Lyon&q=Expert","method":"GET","execution_time":"1461.3 ms","memory_usage":"5.32 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:52:21] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/professionals?filters%5Bcity%5D=Lyon&q=Expert","method":"GET","execution_time":"1461.3 ms","memory_usage":"5.32 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:52:42] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/services?filters%5Bmax_price%5D=3000&q=Integration","method":"GET","execution_time":"1073.04 ms","memory_usage":"3.5 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:52:42] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/services?filters%5Bmax_price%5D=3000&q=Integration","method":"GET","execution_time":"1073.04 ms","memory_usage":"3.5 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:53:04] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/services?q=Integration","method":"GET","execution_time":"1205.49 ms","memory_usage":"5.2 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:53:04] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/services?q=Integration","method":"GET","execution_time":"1205.49 ms","memory_usage":"5.2 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:53:37] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/services?filters%5Bcategories%5D%5B0%5D=Laravel&q=Integration","method":"GET","execution_time":"1956.34 ms","memory_usage":"5.2 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:53:37] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/services?filters%5Bcategories%5D%5B0%5D=Laravel&q=Integration","method":"GET","execution_time":"1956.34 ms","memory_usage":"5.2 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:57:05] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/services?filters%5Bmax_price%5D=3000&q=Integration","method":"GET","execution_time":"3012.6 ms","memory_usage":"3.5 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:57:05] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/services?filters%5Bmax_price%5D=3000&q=Integration","method":"GET","execution_time":"3012.6 ms","memory_usage":"3.5 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:57:20] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/services?filters%5Bmax_price%5D=5000&q=Integration","method":"GET","execution_time":"1004.23 ms","memory_usage":"3.5 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:57:20] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/services?filters%5Bmax_price%5D=5000&q=Integration","method":"GET","execution_time":"1004.23 ms","memory_usage":"3.5 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:57:50] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/professionals?filters%5Bmin_rating%5D=4&q=Expert","method":"GET","execution_time":"1455.14 ms","memory_usage":"5.32 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:57:50] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/professionals?filters%5Bmin_rating%5D=4&q=Expert","method":"GET","execution_time":"1455.14 ms","memory_usage":"5.32 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 20:00:07] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/services?filters%5Bmax_price%5D=3000&q=Integration","method":"GET","execution_time":"1447.19 ms","memory_usage":"5.21 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 20:00:07] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/services?filters%5Bmax_price%5D=3000&q=Integration","method":"GET","execution_time":"1447.19 ms","memory_usage":"5.21 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 20:00:29] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/services?filters%5Bmax_price%5D=2000&q=Integration","method":"GET","execution_time":"3005.7 ms","memory_usage":"3.5 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 20:00:29] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/services?filters%5Bmax_price%5D=2000&q=Integration","method":"GET","execution_time":"3005.7 ms","memory_usage":"3.5 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 20:00:49] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/professionals?filters%5Bmin_rating%5D=4.5&q=Expert","method":"GET","execution_time":"1388.17 ms","memory_usage":"5.32 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 20:00:49] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/professionals?filters%5Bmin_rating%5D=4.5&q=Expert","method":"GET","execution_time":"1388.17 ms","memory_usage":"5.32 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 20:01:45] local.DEBUG: Performance {"url":"http://localhost:8000/api/search?q=Meilisearch","method":"GET","execution_time":"126.55 ms","memory_usage":"1.98 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:01:48] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search?q=Expert","method":"GET","execution_time":"2405.61 ms","memory_usage":"5.57 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:01:48] local.DEBUG: Performance {"url":"http://localhost:8000/api/search?q=Expert","method":"GET","execution_time":"2405.61 ms","memory_usage":"5.57 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:01:50] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search?q=Laravel","method":"GET","execution_time":"2032.17 ms","memory_usage":"5.58 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:01:50] local.DEBUG: Performance {"url":"http://localhost:8000/api/search?q=Laravel","method":"GET","execution_time":"2032.17 ms","memory_usage":"5.58 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:01:53] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/professionals?q=Expert","method":"GET","execution_time":"1873.24 ms","memory_usage":"5.32 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:01:53] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/professionals?q=Expert","method":"GET","execution_time":"1873.24 ms","memory_usage":"5.32 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:01:55] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/services?q=Integration","method":"GET","execution_time":"1146.13 ms","memory_usage":"5.2 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:01:55] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/services?q=Integration","method":"GET","execution_time":"1146.13 ms","memory_usage":"5.2 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:01:57] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/achievements?q=Certified","method":"GET","execution_time":"1319.94 ms","memory_usage":"5.29 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:01:57] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/achievements?q=Certified","method":"GET","execution_time":"1319.94 ms","memory_usage":"5.29 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:01:59] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/professionals?filters%5Bcity%5D=Lyon&q=Expert","method":"GET","execution_time":"1164.74 ms","memory_usage":"5.31 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:01:59] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/professionals?filters%5Bcity%5D=Lyon&q=Expert","method":"GET","execution_time":"1164.74 ms","memory_usage":"5.31 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:00] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/professionals?filters%5Bmin_rating%5D=4.5&q=Expert","method":"GET","execution_time":"1108.84 ms","memory_usage":"5.32 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:00] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/professionals?filters%5Bmin_rating%5D=4.5&q=Expert","method":"GET","execution_time":"1108.84 ms","memory_usage":"5.32 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:02] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/professionals?filters%5Bmax_hourly_rate%5D=80&q=Expert","method":"GET","execution_time":"1118.62 ms","memory_usage":"5.32 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:02] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/professionals?filters%5Bmax_hourly_rate%5D=80&q=Expert","method":"GET","execution_time":"1118.62 ms","memory_usage":"5.32 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:04] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/services?filters%5Bmax_price%5D=3000&q=Integration","method":"GET","execution_time":"1188.81 ms","memory_usage":"5.2 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:04] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/services?filters%5Bmax_price%5D=3000&q=Integration","method":"GET","execution_time":"1188.81 ms","memory_usage":"5.2 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:05] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/services?filters%5Bmax_price%5D=2000&q=Integration","method":"GET","execution_time":"1076.06 ms","memory_usage":"3.5 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:05] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/services?filters%5Bmax_price%5D=2000&q=Integration","method":"GET","execution_time":"1076.06 ms","memory_usage":"3.5 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:07] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/services?filters%5Bcategories%5D%5B0%5D=Laravel&q=Integration","method":"GET","execution_time":"1134.38 ms","memory_usage":"5.2 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:07] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/services?filters%5Bcategories%5D%5B0%5D=Laravel&q=Integration","method":"GET","execution_time":"1134.38 ms","memory_usage":"5.2 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:08] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/suggestions?limit=5&q=Meili","method":"GET","execution_time":"107.77 ms","memory_usage":"1.89 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:11] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/suggestions?limit=3&q=Exp","method":"GET","execution_time":"2217.76 ms","memory_usage":"5.28 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:11] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/suggestions?limit=3&q=Exp","method":"GET","execution_time":"2217.76 ms","memory_usage":"5.28 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:14] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/suggestions?limit=5&q=Lar","method":"GET","execution_time":"2306.3 ms","memory_usage":"5.28 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:14] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/suggestions?limit=5&q=Lar","method":"GET","execution_time":"2306.3 ms","memory_usage":"5.28 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:15] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"22.39 ms","memory_usage":"0.68 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:15] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/popular","method":"GET","execution_time":"29.53 ms","memory_usage":"1.61 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:16] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/metrics/realtime","method":"GET","execution_time":"22.92 ms","memory_usage":"0.69 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:16] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/metrics","method":"GET","execution_time":"49.47 ms","memory_usage":"1.64 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:21] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search?per_page=5&q=test","method":"GET","execution_time":"3693.86 ms","memory_usage":"5.58 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:21] local.DEBUG: Performance {"url":"http://localhost:8000/api/search?per_page=5&q=test","method":"GET","execution_time":"3693.86 ms","memory_usage":"5.58 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:24] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search?q=development&types%5B0%5D=professional_profiles&types%5B1%5D=service_offers","method":"GET","execution_time":"1818.73 ms","memory_usage":"5.54 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 20:02:24] local.DEBUG: Performance {"url":"http://localhost:8000/api/search?q=development&types%5B0%5D=professional_profiles&types%5B1%5D=service_offers","method":"GET","execution_time":"1818.73 ms","memory_usage":"5.54 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:22] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search?q=Meilisearch","method":"GET","execution_time":"9166.72 ms","memory_usage":"5.58 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:22] local.DEBUG: Performance {"url":"http://localhost:8000/api/search?q=Meilisearch","method":"GET","execution_time":"9166.72 ms","memory_usage":"5.58 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:24] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search?q=Expert","method":"GET","execution_time":"2011.09 ms","memory_usage":"5.57 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:24] local.DEBUG: Performance {"url":"http://localhost:8000/api/search?q=Expert","method":"GET","execution_time":"2011.09 ms","memory_usage":"5.57 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:29] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search?q=Laravel","method":"GET","execution_time":"3374.08 ms","memory_usage":"5.58 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:29] local.DEBUG: Performance {"url":"http://localhost:8000/api/search?q=Laravel","method":"GET","execution_time":"3374.08 ms","memory_usage":"5.58 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:30] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/professionals?q=Expert","method":"GET","execution_time":"1062.53 ms","memory_usage":"5.32 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:30] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/professionals?q=Expert","method":"GET","execution_time":"1062.53 ms","memory_usage":"5.32 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:32] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/services?q=Integration","method":"GET","execution_time":"1055.47 ms","memory_usage":"5.2 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:32] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/services?q=Integration","method":"GET","execution_time":"1055.47 ms","memory_usage":"5.2 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:33] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/achievements?q=Certified","method":"GET","execution_time":"1059.6 ms","memory_usage":"5.29 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:33] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/achievements?q=Certified","method":"GET","execution_time":"1059.6 ms","memory_usage":"5.29 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:35] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/professionals?filters%5Bcity%5D=Lyon&q=Expert","method":"GET","execution_time":"1027.22 ms","memory_usage":"5.31 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:35] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/professionals?filters%5Bcity%5D=Lyon&q=Expert","method":"GET","execution_time":"1027.22 ms","memory_usage":"5.31 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:37] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/professionals?filters%5Bmin_rating%5D=4.5&q=Expert","method":"GET","execution_time":"1156.82 ms","memory_usage":"5.32 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:37] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/professionals?filters%5Bmin_rating%5D=4.5&q=Expert","method":"GET","execution_time":"1156.82 ms","memory_usage":"5.32 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:39] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/professionals?filters%5Bmax_hourly_rate%5D=80&q=Expert","method":"GET","execution_time":"1456.83 ms","memory_usage":"5.32 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:39] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/professionals?filters%5Bmax_hourly_rate%5D=80&q=Expert","method":"GET","execution_time":"1456.83 ms","memory_usage":"5.32 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:40] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/services?filters%5Bmax_price%5D=3000&q=Integration","method":"GET","execution_time":"1162.53 ms","memory_usage":"5.2 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:40] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/services?filters%5Bmax_price%5D=3000&q=Integration","method":"GET","execution_time":"1162.53 ms","memory_usage":"5.2 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:42] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/services?filters%5Bmax_price%5D=2000&q=Integration","method":"GET","execution_time":"1036.21 ms","memory_usage":"3.5 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:42] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/services?filters%5Bmax_price%5D=2000&q=Integration","method":"GET","execution_time":"1036.21 ms","memory_usage":"3.5 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:44] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/services?filters%5Bcategories%5D%5B0%5D=Laravel&q=Integration","method":"GET","execution_time":"1187.99 ms","memory_usage":"5.2 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:44] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/services?filters%5Bcategories%5D%5B0%5D=Laravel&q=Integration","method":"GET","execution_time":"1187.99 ms","memory_usage":"5.2 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:46] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/suggestions?limit=5&q=Meili","method":"GET","execution_time":"1811.88 ms","memory_usage":"5.28 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:46] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/suggestions?limit=5&q=Meili","method":"GET","execution_time":"1811.88 ms","memory_usage":"5.28 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:49] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/suggestions?limit=3&q=Exp","method":"GET","execution_time":"2006.74 ms","memory_usage":"5.28 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:49] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/suggestions?limit=3&q=Exp","method":"GET","execution_time":"2006.74 ms","memory_usage":"5.28 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:51] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/suggestions?limit=5&q=Lar","method":"GET","execution_time":"1778.51 ms","memory_usage":"5.28 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:51] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/suggestions?limit=5&q=Lar","method":"GET","execution_time":"1778.51 ms","memory_usage":"5.28 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:52] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"189.36 ms","memory_usage":"2.61 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:53] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/popular","method":"GET","execution_time":"31.8 ms","memory_usage":"1.61 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:53] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/metrics/realtime","method":"GET","execution_time":"21.67 ms","memory_usage":"0.69 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:54] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/metrics","method":"GET","execution_time":"45 ms","memory_usage":"1.64 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:56] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search?per_page=5&q=test","method":"GET","execution_time":"1778.75 ms","memory_usage":"5.58 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:56] local.DEBUG: Performance {"url":"http://localhost:8000/api/search?per_page=5&q=test","method":"GET","execution_time":"1778.75 ms","memory_usage":"5.58 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:58] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search?q=development&types%5B0%5D=professional_profiles&types%5B1%5D=service_offers","method":"GET","execution_time":"1497.79 ms","memory_usage":"5.54 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:11:58] local.DEBUG: Performance {"url":"http://localhost:8000/api/search?q=development&types%5B0%5D=professional_profiles&types%5B1%5D=service_offers","method":"GET","execution_time":"1497.79 ms","memory_usage":"5.54 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 23:21:04] testing.DEBUG: Performance {"url":"http://localhost/api/search?q=Laravel","method":"GET","execution_time":"150.73 ms","memory_usage":"1.91 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"Symfony"} 
