[2025-07-08 14:10:52] local.DEBUG: Performance {"url":"http://localhost:8000/api/health-check","method":"GET","execution_time":"31.08 ms","memory_usage":"0.74 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:11:13] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/view","method":"POST","execution_time":"15.5 ms","memory_usage":"0.71 MB","status_code":500,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:11:29] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/view/stats","method":"GET","execution_time":"39.13 ms","memory_usage":"0.86 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:11:48] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/like","method":"POST","execution_time":"38.64 ms","memory_usage":"1.14 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:12:12] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/like/status","method":"GET","execution_time":"27.77 ms","memory_usage":"1.13 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:12:31] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/like/toggle","method":"POST","execution_time":"37.75 ms","memory_usage":"1.13 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:12:53] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/like","method":"POST","execution_time":"48.95 ms","memory_usage":"1.14 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:13:39] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/like","method":"DELETE","execution_time":"37.31 ms","memory_usage":"1.13 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:13:57] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/view","method":"POST","execution_time":"13.02 ms","memory_usage":"0.71 MB","status_code":500,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:14:45] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/view/status","method":"GET","execution_time":"14.42 ms","memory_usage":"0.71 MB","status_code":500,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:15:26] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/view","method":"POST","execution_time":"41.34 ms","memory_usage":"0.85 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:15:38] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/view/status","method":"GET","execution_time":"22.77 ms","memory_usage":"0.85 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:15:53] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/view/stats","method":"GET","execution_time":"40.1 ms","memory_usage":"0.86 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:16:46] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/1/view/stats","method":"GET","execution_time":"39.11 ms","memory_usage":"0.86 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 14:17:15] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/1/like","method":"POST","execution_time":"62.47 ms","memory_usage":"1.28 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:17:15] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/2/like","method":"DELETE","execution_time":"13.63 ms","memory_usage":"0.01 MB","status_code":200,"user_id":3,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:17:16] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/3/like/toggle","method":"POST","execution_time":"26.8 ms","memory_usage":"0.02 MB","status_code":200,"user_id":5,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:17:16] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/3/like/toggle","method":"POST","execution_time":"12.07 ms","memory_usage":"0.01 MB","status_code":200,"user_id":5,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:17:17] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/5/like/status","method":"GET","execution_time":"33.72 ms","memory_usage":"0.01 MB","status_code":200,"user_id":9,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:17:17] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/5/like/status","method":"GET","execution_time":"4.38 ms","memory_usage":"0.01 MB","status_code":200,"user_id":9,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:18:19] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/1/like","method":"POST","execution_time":"43.67 ms","memory_usage":"1.28 MB","status_code":200,"user_id":1,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:18:19] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/2/like","method":"DELETE","execution_time":"9.72 ms","memory_usage":"0.01 MB","status_code":200,"user_id":3,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:18:20] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/3/like/toggle","method":"POST","execution_time":"17.91 ms","memory_usage":"0.02 MB","status_code":200,"user_id":5,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:18:20] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/3/like/toggle","method":"POST","execution_time":"10.09 ms","memory_usage":"0.01 MB","status_code":200,"user_id":5,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:18:20] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/5/like/status","method":"GET","execution_time":"10.91 ms","memory_usage":"0.01 MB","status_code":200,"user_id":9,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 14:18:20] testing.DEBUG: Performance {"url":"http://localhost/api/professionals/5/like/status","method":"GET","execution_time":"3.44 ms","memory_usage":"0.01 MB","status_code":200,"user_id":9,"ip":"127.0.0.1","user_agent":"Symfony"} 
[2025-07-08 17:43:10] local.DEBUG: Performance {"url":"http://localhost:8000/api/professionals/6/like/status","method":"GET","execution_time":"94.17 ms","memory_usage":"1.13 MB","status_code":200,"user_id":11,"ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 18:56:33] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"275.81 ms","memory_usage":"2.6 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:12:04] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"2120.83 ms","memory_usage":"4.77 MB","status_code":500,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:12:04] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"2120.83 ms","memory_usage":"4.77 MB","status_code":500,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:14:26] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"273.15 ms","memory_usage":"2.61 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:14:43] local.INFO: Requête lente détectée {"url":"http://localhost:8000/api/search/suggestions?limit=5&q=Lar","method":"GET","execution_time":"2422.27 ms","memory_usage":"3.48 MB","status_code":500,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:14:43] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/suggestions?limit=5&q=Lar","method":"GET","execution_time":"2422.27 ms","memory_usage":"3.48 MB","status_code":500,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:17:17] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"40.12 ms","memory_usage":"0.68 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:17:18] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/popular","method":"GET","execution_time":"88.1 ms","memory_usage":"1.61 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:17:19] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/metrics/realtime","method":"GET","execution_time":"24.46 ms","memory_usage":"0.69 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:17:19] local.DEBUG: Performance {"url":"http://localhost:8000/api/search","method":"GET","execution_time":"37.99 ms","memory_usage":"1.43 MB","status_code":302,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:17:20] local.DEBUG: Performance {"url":"http://localhost:8000/api/search?q=a","method":"GET","execution_time":"28.84 ms","memory_usage":"1.7 MB","status_code":302,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:17:20] local.DEBUG: Performance {"url":"http://localhost:8000/api/search?q=test&types%5B0%5D=invalid_type","method":"GET","execution_time":"29.59 ms","memory_usage":"1.7 MB","status_code":302,"user_id":"guest","ip":"127.0.0.1","user_agent":null} 
[2025-07-08 19:19:56] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"32.32 ms","memory_usage":"0.68 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:21:38] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"35.77 ms","memory_usage":"0.68 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:22:21] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/metrics/realtime","method":"GET","execution_time":"21.38 ms","memory_usage":"0.69 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
[2025-07-08 19:23:51] local.DEBUG: Performance {"url":"http://localhost:8000/api/search/stats","method":"GET","execution_time":"51.68 ms","memory_usage":"0.68 MB","status_code":200,"user_id":"guest","ip":"127.0.0.1","user_agent":"curl/8.12.1"} 
