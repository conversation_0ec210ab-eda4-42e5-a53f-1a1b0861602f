<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Lara<PERSON>\Scout\Searchable;

class Achievement extends Model
{
    use HasFactory, Searchable;

    protected $fillable = [
        'professional_profile_id',
        'title',
        'organization',
        'date_obtained',
        'description',
        'file_path', // Maintenu pour la rétrocompatibilité
        'files', // Nouveau champ pour plusieurs fichiers
        'achievement_url',
    ];

    protected $casts = [
        'date_obtained' => 'date',
        'files' => 'array', // Cast pour le nouveau champ files
    ];

    /**
     * Get the freelance profile that owns the achievement.
     */
    public function professionalProfile(): BelongsTo
    {
        return $this->belongsTo(ProfessionalProfile::class);
    }

    /**
     * Get the indexable data array for the model.
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'organization' => $this->organization,
            'description' => $this->description,
            'date_obtained' => $this->date_obtained?->format('Y-m-d'),
            'achievement_url' => $this->achievement_url,
            'professional_profile_id' => $this->professional_profile_id,
            'professional_name' => $this->professionalProfile ?
                $this->professionalProfile->first_name . ' ' . $this->professionalProfile->last_name : null,
            'type' => 'achievement',
        ];
    }

    /**
     * Get the name of the index associated with the model.
     */
    public function searchableAs(): string
    {
        return 'achievements_index';
    }

    /**
     * Determine if the model should be searchable.
     */
    public function shouldBeSearchable(): bool
    {
        return !empty($this->title) && !empty($this->organization);
    }
}
