# Service Offer Workflow

## Vue d'ensemble

Ce document décrit le workflow complet des offres de service (Service Offers) dans l'application Hi3D, incluant les processus de création, gestion, recherche et interaction.

## Architecture

### Modèles principaux
- **ServiceOffer** : Modèle principal des offres de service
- **User** : Utilisateur propriétaire de l'offre
- **FreelanceProfile** : Profil freelance associé à l'utilisateur

### Contrôleurs
- **ServiceOfferController** : Gestion CRUD des offres de service
- **ServiceMessageController** : Gestion des messages liés aux services

## Workflow de création d'une offre de service

```mermaid
graph TD
    A[Utilisateur connecté] --> B[Formulaire de création]
    B --> C[Validation des données]
    C --> D{Validation OK?}
    D -->|Non| E[Retour erreurs]
    D -->|Oui| F[Upload des fichiers]
    F --> G[Création en base]
    G --> H[Retour succès avec ressource]
    
    E --> B
```

### Étapes détaillées

1. **Authentification requise**
   - L'utilisateur doit être connecté (`auth:sanctum`)
   - Vérification du statut vérifié

2. **Validation des données** (StoreServiceOfferRequest)
   - `title` : requis, string, max 255 caractères
   - `description` : requis, texte
   - `price` : requis, numérique, min 0
   - `execution_time` : optionnel, string
   - `concepts` : optionnel, entier
   - `revisions` : optionnel, entier
   - `categories` : optionnel, array de strings
   - `is_private` : optionnel, boolean (défaut: false)
   - `files` : optionnel, array de fichiers (max 2MB chacun)

3. **Gestion des fichiers**
   - Stockage dans `service_offer_files` (disque public)
   - Métadonnées sauvegardées : path, nom original, type MIME, taille
   - Gestion d'erreurs pour chaque fichier

4. **Création en base**
   - Attribution automatique du `user_id`
   - Statut par défaut : 'active'
   - Conversion des catégories en JSON

## Workflow de mise à jour

```mermaid
graph TD
    A[Demande de mise à jour] --> B[Vérification autorisation]
    B --> C{Propriétaire?}
    C -->|Non| D[Erreur 403]
    C -->|Oui| E[Validation données]
    E --> F[Gestion fichiers]
    F --> G[Suppression anciens fichiers]
    G --> H[Upload nouveaux fichiers]
    H --> I[Mise à jour en base]
    I --> J[Retour succès]
```

### Règles d'autorisation
- Seul le propriétaire peut modifier son offre
- Vérification : `$serviceOffer->user_id == auth()->id()`

## Workflow de recherche et filtrage

### Recherche textuelle
- Utilise Laravel Scout pour la recherche full-text
- Champs indexés : `title`, `description`
- Pagination : 10 résultats par page

### Filtrage avancé
```mermaid
graph TD
    A[Requête de filtrage] --> B[Construction query builder]
    B --> C[Filtre recherche textuelle]
    C --> D[Filtre par catégorie]
    D --> E[Filtre par prix]
    E --> F[Filtre par temps d'exécution]
    F --> G[Tri des résultats]
    G --> H[Pagination]
    H --> I[Retour résultats]
```

#### Filtres disponibles
- **search** : Recherche dans titre et description
- **category** : Filtrage par catégorie (JSON search)
- **min_price** / **max_price** : Fourchette de prix
- **execution_time** : Temps d'exécution spécifique
- **sort_by** : Tri (price_asc, price_desc, latest, oldest)

## Workflow de consultation

### Affichage public
- Route : `/service-offers/{id}/public`
- Accessible sans authentification
- Incrémentation du compteur de vues
- Chargement des relations : user, freelanceProfile

### Affichage privé
- Authentification requise
- Vérification des permissions pour les offres privées
- Accès complet aux métadonnées

## Workflow de téléchargement de fichiers

```mermaid
graph TD
    A[Demande téléchargement] --> B[Vérification existence offre]
    B --> C[Vérification fichier]
    C --> D{Fichier existe?}
    D -->|Non| E[Erreur 404]
    D -->|Oui| F[Téléchargement]
```

## Statuts des offres

- **active** : Offre active et visible
- **inactive** : Offre désactivée
- **draft** : Brouillon (non publié)
- **completed** : Offre terminée

## Gestion des erreurs

### Erreurs courantes
- **422** : Erreurs de validation
- **403** : Non autorisé (modification par non-propriétaire)
- **404** : Offre non trouvée
- **500** : Erreurs serveur (upload, base de données)

### Logging
- Toutes les erreurs sont loggées avec contexte
- Messages d'erreur utilisateur génériques pour la sécurité

## API Endpoints

### Routes publiques
- `GET /api/service-offers` : Liste paginée
- `GET /api/service-offers/{id}/public` : Consultation publique
- `GET /api/professionals/{id}/service-offers` : Offres d'un professionnel

### Routes authentifiées
- `POST /api/service-offers` : Création
- `GET /api/service-offers/{id}` : Consultation détaillée
- `POST /api/service-offers/{id}` : Mise à jour
- `DELETE /api/service-offers/{id}` : Suppression
- `GET /api/service-offers/filter` : Filtrage avancé
- `GET /api/service-offers/{id}/download` : Téléchargement fichier

## Intégrations

### Système de messagerie
- Messages liés aux offres via ServiceMessageController
- Conversations entre clients et prestataires

### Tableau de bord
- Statistiques des offres dans DashboardController
- Suivi des performances (vues, likes, rating)

## Bonnes pratiques

1. **Sécurité**
   - Validation stricte des inputs
   - Vérification des autorisations
   - Sanitisation des fichiers uploadés

2. **Performance**
   - Pagination systématique
   - Eager loading des relations
   - Cache pour les recherches fréquentes

3. **UX**
   - Messages d'erreur clairs
   - Feedback utilisateur pour les actions longues
   - Prévisualisation des fichiers

## Évolutions futures

- Système de favoris
- Notifications push pour nouvelles offres
- Système de recommandations basé sur l'historique
- Intégration paiement pour réservation d'offres
