import React from 'react';
import { useNavigate } from 'react-router-dom';

type GalleryItem = {
  id: number;
  title: string;
  author: string;
  authorAvatar: string;
  isPro?: boolean;
  likes: number;
  views: string;
  image: string;
};

type Props = {
  items: GalleryItem[];
};

const MyWork: React.FC<Props> = ({ items }) => {
  const navigate = useNavigate();
  return (
    <div className="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 sm:gap-8 px-2 sm:px-0 justify-items-center">
      {items.map(item => (
        <div
          key={item.id}
          className="w-[90vw] max-w-[315px] bg-white rounded-lg overflow-hidden m-auto relative shadow-none flex flex-col"
        >
          <div
            style={{
              backgroundImage: `url(${item.image})`,
            }}
            className="bg-[#2d241b] bg-cover bg-center w-full h-[220px] rounded-lg"
            aria-label={item.title}
            role="img"
          />
          <div
            className="w-full flex justify-between items-center px-0 pt-[18px] pb-2.5 min-h-[60px] text-[12px]" style={{ fontFamily: 'Arial, sans-serif' }}
          >
            <div className="flex items-center gap-2.5">
              <img
                src={item.authorAvatar}
                alt={item.author}
                className="w-8 h-8 rounded-full object-cover border-2 border-[#eee]"
              />
              <span className="font-semibold truncate max-w-[90px]">{item.author}</span>
              {item.isPro && (
                <span className="bg-[#f3f3f3] text-[#888] font-bold rounded px-2 py-0.5 ml-1">PRO</span>
              )}
            </div>
            <div className="flex items-center gap-4 text-[#888]">
              <span className="flex items-center gap-1">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="#b3b3b3" className="mr-0.5"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41 0.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>
                {item.likes}
              </span>
              <span className="flex items-center gap-1">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="#b3b3b3" className="mr-0.5"><path d="M12 5c-7 0-10 7-10 7s3 7 10 7 10-7 10-7-3-7-10-7zm0 12c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8a3 3 0 100 6 3 3 0 000-6z"/></svg>
                {item.views}
              </span>
            </div>
          </div>
        </div>
      ))}
      {/* Add work  card */}
      <div
        className="w-[90vw] max-w-[315px] bg-white rounded-lg overflow-hidden m-auto relative shadow-none flex flex-col border-2 border-dashed border-[#222] min-h-[220px] items-center justify-center cursor-pointer"
        onClick={() => navigate('/dashboard/create-project')}
        tabIndex={0}
        role="button"
        aria-label="Add work"
        onKeyDown={e => { if (e.key === 'Enter' || e.key === ' ') navigate('/dashboard/create-project'); }}
      >
        <div style={{ fontSize: 32, marginBottom: 8, color: '#222' }}>+</div>
        <div style={{ fontSize: 18, color: '#222' }}>Add work</div>
      </div>
      {/* <div className="w-[90vw] max-w-[315px] bg-white rounded-lg overflow-hidden m-auto relative shadow-none flex flex-col border-2 border-dashed border-[#222] min-h-[220px] items-center justify-center cursor-pointer">
        <div style={{ fontSize: 32, marginBottom: 8, color: '#222' }}>+</div>
        <div style={{ fontSize: 18, color: '#222' }}>Add work</div>
      </div> */}
    </div>
  );
};

export default MyWork; 